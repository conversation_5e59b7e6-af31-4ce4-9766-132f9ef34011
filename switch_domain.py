#!/usr/bin/env python3
"""
Utility script to switch between different SYSTEM_SCOPE domains.
Provides an easy way to change the domain configuration.
"""

import os
import sys
import argparse
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from domain_config import get_available_domains, get_domain_config, get_multi_domain_config, validate_domain

def read_env_file():
    """Read current .env file"""
    env_file = Path('.env')
    if not env_file.exists():
        return {}
    
    env_vars = {}
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
    
    return env_vars

def write_env_file(env_vars):
    """Write updated .env file"""
    env_file = Path('.env')
    
    # Read original file to preserve comments and structure
    original_lines = []
    if env_file.exists():
        with open(env_file, 'r') as f:
            original_lines = f.readlines()
    
    # Update SYSTEM_SCOPE line or add it
    updated_lines = []
    scope_updated = False
    
    for line in original_lines:
        if line.strip().startswith('SYSTEM_SCOPE='):
            updated_lines.append(f"SYSTEM_SCOPE={env_vars['SYSTEM_SCOPE']}\n")
            scope_updated = True
        else:
            updated_lines.append(line)
    
    # Add SYSTEM_SCOPE if not found
    if not scope_updated:
        # Find a good place to insert it (after security settings)
        insert_index = len(updated_lines)
        for i, line in enumerate(updated_lines):
            if 'MAX_CONCURRENT_DOCS' in line:
                insert_index = i + 1
                break
        
        updated_lines.insert(insert_index, "\n# Domain Configuration\n")
        updated_lines.insert(insert_index + 1, f"SYSTEM_SCOPE={env_vars['SYSTEM_SCOPE']}\n")
    
    # Write updated file
    with open(env_file, 'w') as f:
        f.writelines(updated_lines)

def show_current_domain():
    """Show current domain configuration"""
    env_vars = read_env_file()
    current_scope = env_vars.get('SYSTEM_SCOPE', 'AUTOMOTIVE')

    # Parse multiple domains
    scopes = [scope.strip() for scope in current_scope.split(',') if scope.strip()]

    if len(scopes) == 1:
        print(f"🎯 Current Domain: {scopes[0]}")
        if validate_domain(scopes[0]):
            domain_config = get_domain_config(scopes[0])
            print(f"   Name: {domain_config.display_name}")
            print(f"   Description: {domain_config.description}")
            print(f"   Keywords: {len(domain_config.keywords)}")
        else:
            print("   ⚠️  Invalid domain configuration!")
    else:
        print(f"🎯 Current Domains: {', '.join(scopes)}")

        # Validate each domain
        valid_domains = []
        for scope in scopes:
            if validate_domain(scope):
                valid_domains.append(scope)
            else:
                print(f"   ⚠️  Invalid domain: {scope}")

        if valid_domains:
            combined_config = get_multi_domain_config(valid_domains)
            print(f"   Combined Name: {combined_config.display_name}")
            print(f"   Combined Keywords: {len(combined_config.keywords)}")
            print(f"   Individual Domains:")
            for domain in valid_domains:
                domain_config = get_domain_config(domain)
                print(f"     - {domain}: {domain_config.display_name}")

def list_available_domains():
    """List all available domains"""
    print("📋 Available Domains:")
    print()
    
    domains = get_available_domains()
    for domain in domains:
        domain_config = get_domain_config(domain)
        print(f"  {domain}")
        print(f"    Name: {domain_config.display_name}")
        print(f"    Description: {domain_config.description}")
        print()

def switch_domain(new_domains_str):
    """Switch to new domain(s) - supports single domain or comma-separated list"""
    # Parse domains
    new_domains = [domain.strip().upper() for domain in new_domains_str.split(',') if domain.strip()]

    if not new_domains:
        print("❌ No domains specified")
        return False

    # Validate all domains
    invalid_domains = []
    valid_domains = []

    for domain in new_domains:
        if validate_domain(domain):
            valid_domains.append(domain)
        else:
            invalid_domains.append(domain)

    if invalid_domains:
        print(f"❌ Invalid domains: {', '.join(invalid_domains)}")
        print("Available domains:", ", ".join(get_available_domains()))
        if not valid_domains:
            return False
        print(f"⚠️  Proceeding with valid domains only: {', '.join(valid_domains)}")

    # Read current env
    env_vars = read_env_file()
    old_domains_str = env_vars.get('SYSTEM_SCOPE', 'AUTOMOTIVE')
    old_domains = [d.strip().upper() for d in old_domains_str.split(',') if d.strip()]

    # Check if already using these domains
    if set(old_domains) == set(valid_domains):
        if len(valid_domains) == 1:
            print(f"✅ Already using domain: {valid_domains[0]}")
        else:
            print(f"✅ Already using domains: {', '.join(valid_domains)}")
        return True

    # Update domains
    new_scope_str = ','.join(valid_domains)
    env_vars['SYSTEM_SCOPE'] = new_scope_str
    write_env_file(env_vars)

    # Show confirmation
    if len(valid_domains) == 1:
        domain_config = get_domain_config(valid_domains[0])
        print(f"✅ Switched from {old_domains_str} to {valid_domains[0]}")
        print(f"   New domain: {domain_config.display_name}")
        print(f"   Description: {domain_config.description}")
    else:
        combined_config = get_multi_domain_config(valid_domains)
        print(f"✅ Switched from {old_domains_str} to multi-domain: {', '.join(valid_domains)}")
        print(f"   Combined name: {combined_config.display_name}")
        print(f"   Individual domains:")
        for domain in valid_domains:
            domain_config = get_domain_config(domain)
            print(f"     - {domain}: {domain_config.display_name}")

    print()
    print("⚠️  Remember to restart the system for changes to take effect!")

    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Switch between different SYSTEM_SCOPE domains",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python switch_domain.py --current                    # Show current domain(s)
  python switch_domain.py --list                       # List available domains
  python switch_domain.py --switch FINANCE             # Switch to Finance domain
  python switch_domain.py --switch automotive          # Switch to Automotive (case insensitive)
  python switch_domain.py --switch "FINANCE,HR"        # Switch to multiple domains
  python switch_domain.py --switch "automotive,it_support,finance"  # Multiple domains
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--current', action='store_true', help='Show current domain')
    group.add_argument('--list', action='store_true', help='List available domains')
    group.add_argument('--switch', metavar='DOMAIN(S)', help='Switch to specified domain(s) - single domain or comma-separated list')
    
    args = parser.parse_args()
    
    if args.current:
        show_current_domain()
    elif args.list:
        list_available_domains()
    elif args.switch:
        success = switch_domain(args.switch)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
