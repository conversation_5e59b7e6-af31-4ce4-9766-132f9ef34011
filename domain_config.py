"""
Domain-specific configurations for the AI chatbot system.
Defines keywords, validation rules, and prompts for different business domains.
"""

from typing import Dict, Set, List, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class DomainConfig:
    """Configuration for a specific business domain"""
    name: str
    display_name: str
    description: str
    keywords: Set[str]
    non_keywords: Set[str]
    technical_keywords: Set[str]
    system_prompt: str
    rejection_message: str
    file_extensions: List[str] = None
    
    def __post_init__(self):
        if self.file_extensions is None:
            self.file_extensions = ['.pdf']

# Domain configurations
DOMAIN_CONFIGS = {
    'AUTOMOTIVE': DomainConfig(
        name='AUTOMOTIVE',
        display_name='Automotive & Mechanical',
        description='Automotive, motorcycle, and mechanical technical assistance',
        keywords={
            # Veicoli
            'motore', 'moto', 'scooter', 'automobile', 'auto', 'veicolo', 'motociclo',
            'quad', 'atv', 'ciclomotore', 'motorino', 'vespa', 'harley', 'ducati',
            
            # Componenti meccanici
            'freni', 'freno', 'disco', 'pastiglie', 'pinze', 'cilindretto',
            'sospensioni', 'ammortizzatori', 'forcella', 'monoammortizzatore',
            'trasmissione', 'catena', 'corona', 'pignone', 'cambio', 'frizione',
            'carburatore', 'iniezione', 'iniettori', 'pompa', 'benzina', 'gasolio',
            'candela', 'bobina', 'accensione', 'batteria', 'alternatore', 'motorino',
            'avviamento', 'starter', 'radiatore', 'termostato', 'liquido', 'refrigerante',
            
            # Manutenzione
            'tagliando', 'manutenzione', 'sostituzione', 'controllo', 'regolazione',
            'riparazione', 'revisione', 'olio', 'filtro', 'aria', 'carburante',
            'coppia', 'serraggio', 'nm', 'chiave', 'dinamometrica',
            
            # Diagnostica
            'errore', 'codice', 'diagnostica', 'centralina', 'ecu', 'obd', 'scanner',
            'spia', 'warning', 'check', 'engine', 'abs', 'esp', 'tcs',
            
            # Pneumatici e ruote
            'pneumatico', 'gomma', 'ruota', 'cerchio', 'pressione', 'battistrada',
            'equilibratura', 'convergenza', 'campanatura'
        },
        non_keywords={
            'programmazione', 'software', 'codice', 'html', 'css', 'javascript', 'python',
            'java', 'database', 'sql', 'server', 'network', 'computer', 'pc',
            'medicina', 'salute', 'farmaco', 'terapia', 'diagnosi', 'sintomo',
            'finanza', 'banca', 'investimento', 'azione', 'borsa', 'trading',
            'cucina', 'ricetta', 'ingrediente', 'cucinare', 'forno', 'pentola'
        },
        technical_keywords={
            'cilindrata', 'cavalli', 'cv', 'kw', 'coppia', 'rpm', 'giri',
            'carburazione', 'miscela', 'rapporto', 'compressione', 'valvole',
            'distribuzione', 'albero', 'camme', 'pistone', 'biella', 'manovella'
        },
        system_prompt="""Sei un assistente tecnico specializzato in documentazione automotive e meccanica.

ISTRUZIONI:
1. Rispondi in italiano con terminologia tecnica precisa
2. Usa le informazioni fornite per rispondere alla domanda
3. Se le informazioni non sono sufficienti, dillo chiaramente
4. Mantieni un tono professionale e tecnico
5. Includi dettagli specifici come coppie di serraggio, procedure, codici errore
6. Non inventare informazioni non presenti nei documenti""",
        rejection_message="Mi dispiace, ma posso assistere solo con questioni tecniche relative a veicoli a motore (motocicli, scooter, automobili, ecc.). La tua domanda sembra essere al di fuori del mio ambito di competenza automotive/meccanico. Potresti riformulare la domanda in relazione a problemi tecnici, manutenzione o riparazione di veicoli?"
    ),
    
    'FINANCE': DomainConfig(
        name='FINANCE',
        display_name='Finance & Banking',
        description='Financial services, banking, and investment assistance',
        keywords={
            # Servizi bancari
            'banca', 'conto', 'corrente', 'deposito', 'risparmio', 'prestito',
            'mutuo', 'finanziamento', 'credito', 'debito', 'rata', 'interesse',
            'tasso', 'spread', 'commissione', 'bonifico', 'versamento',
            
            # Investimenti
            'investimento', 'portafoglio', 'azione', 'obbligazione', 'etf',
            'fondo', 'comune', 'rendimento', 'dividendo', 'capitale', 'borsa',
            'mercato', 'trading', 'broker', 'quotazione', 'volatilità',
            
            # Assicurazioni
            'assicurazione', 'polizza', 'premio', 'sinistro', 'risarcimento',
            'copertura', 'franchigia', 'malus', 'bonus', 'rc', 'auto',
            
            # Fiscale
            'tasse', 'imposte', 'iva', 'irpef', 'dichiarazione', 'redditi',
            'detrazioni', 'deduzioni', 'codice', 'fiscale', 'partita', 'iva'
        },
        non_keywords={
            'motore', 'freni', 'olio', 'manutenzione', 'riparazione',
            'programmazione', 'software', 'html', 'css', 'javascript',
            'medicina', 'farmaco', 'terapia', 'sintomo', 'diagnosi'
        },
        technical_keywords={
            'spread', 'yield', 'duration', 'rating', 'benchmark', 'hedge',
            'derivati', 'futures', 'options', 'swap', 'forward'
        },
        system_prompt="""Sei un assistente specializzato in servizi finanziari e bancari.

ISTRUZIONI:
1. Rispondi in italiano con terminologia finanziaria precisa
2. Usa le informazioni fornite per rispondere alla domanda
3. Se le informazioni non sono sufficienti, dillo chiaramente
4. Mantieni un tono professionale e competente
5. Includi dettagli specifici su prodotti, tassi, condizioni
6. Non fornire consigli di investimento personalizzati
7. Suggerisci sempre di consultare un consulente qualificato per decisioni importanti""",
        rejection_message="Mi dispiace, ma posso assistere solo con questioni relative a servizi finanziari, bancari e assicurativi. La tua domanda sembra essere al di fuori del mio ambito di competenza finanziaria. Potresti riformulare la domanda in relazione a prodotti bancari, investimenti o servizi finanziari?"
    ),
    
    'HR': DomainConfig(
        name='HR',
        display_name='Human Resources',
        description='Human resources, recruitment, and employee management',
        keywords={
            # Gestione personale
            'dipendente', 'lavoratore', 'personale', 'staff', 'risorsa', 'umana',
            'assunzione', 'selezione', 'colloquio', 'recruiting', 'candidato',
            'cv', 'curriculum', 'esperienza', 'competenza', 'skill',
            
            # Contratti e retribuzione
            'contratto', 'lavoro', 'stipendio', 'salario', 'retribuzione',
            'busta', 'paga', 'contributi', 'tredicesima', 'quattordicesima',
            'ferie', 'permessi', 'malattia', 'maternità', 'paternità',
            
            # Formazione e sviluppo
            'formazione', 'training', 'corso', 'sviluppo', 'carriera',
            'promozione', 'valutazione', 'performance', 'obiettivi',
            'feedback', 'coaching', 'mentoring',
            
            # Normative
            'ccnl', 'contratto', 'collettivo', 'sindacato', 'diritti',
            'doveri', 'disciplinare', 'licenziamento', 'dimissioni'
        },
        non_keywords={
            'motore', 'freni', 'manutenzione', 'riparazione',
            'investimento', 'azione', 'borsa', 'trading',
            'programmazione', 'software', 'database', 'server'
        },
        technical_keywords={
            'kpi', 'performance', 'assessment', 'talent', 'management',
            'onboarding', 'offboarding', 'engagement', 'retention'
        },
        system_prompt="""Sei un assistente specializzato in risorse umane e gestione del personale.

ISTRUZIONI:
1. Rispondi in italiano con terminologia HR precisa
2. Usa le informazioni fornite per rispondere alla domanda
3. Se le informazioni non sono sufficienti, dillo chiaramente
4. Mantieni un tono professionale e rispettoso
5. Includi riferimenti normativi quando appropriato
6. Non fornire consigli legali specifici
7. Suggerisci di consultare esperti legali per questioni complesse""",
        rejection_message="Mi dispiace, ma posso assistere solo con questioni relative alle risorse umane, gestione del personale e normative del lavoro. La tua domanda sembra essere al di fuori del mio ambito di competenza HR. Potresti riformulare la domanda in relazione a gestione dipendenti, contratti di lavoro o procedure aziendali?"
    ),

    'IT_SUPPORT': DomainConfig(
        name='IT_SUPPORT',
        display_name='IT Support & Technical Assistance',
        description='Information technology support, troubleshooting, and technical assistance',
        keywords={
            # Hardware
            'computer', 'pc', 'laptop', 'desktop', 'server', 'workstation',
            'processore', 'cpu', 'memoria', 'ram', 'disco', 'hard', 'ssd',
            'scheda', 'madre', 'video', 'grafica', 'alimentatore', 'ventola',
            'monitor', 'schermo', 'tastiera', 'mouse', 'stampante', 'scanner',

            # Software
            'sistema', 'operativo', 'windows', 'linux', 'macos', 'software',
            'programma', 'applicazione', 'app', 'driver', 'aggiornamento',
            'installazione', 'configurazione', 'licenza', 'antivirus',

            # Networking
            'rete', 'network', 'internet', 'wifi', 'ethernet', 'router',
            'switch', 'firewall', 'vpn', 'ip', 'dns', 'dhcp', 'tcp',
            'connessione', 'banda', 'velocità', 'latenza', 'ping',

            # Troubleshooting
            'errore', 'problema', 'bug', 'crash', 'freeze', 'lento',
            'riavvio', 'spegnimento', 'backup', 'ripristino', 'recovery',
            'diagnostica', 'log', 'evento', 'performance', 'ottimizzazione'
        },
        non_keywords={
            'motore', 'freni', 'manutenzione', 'riparazione', 'veicolo',
            'investimento', 'azione', 'borsa', 'banca', 'prestito',
            'dipendente', 'contratto', 'stipendio', 'assunzione'
        },
        technical_keywords={
            'tcp/ip', 'ssl', 'https', 'api', 'database', 'sql',
            'virtualization', 'cloud', 'saas', 'iaas', 'paas'
        },
        system_prompt="""Sei un assistente specializzato in supporto tecnico IT e risoluzione problemi informatici.

ISTRUZIONI:
1. Rispondi in italiano con terminologia tecnica IT precisa
2. Usa le informazioni fornite per rispondere alla domanda
3. Se le informazioni non sono sufficienti, dillo chiaramente
4. Mantieni un tono professionale e competente
5. Fornisci procedure step-by-step quando appropriato
6. Includi comandi, configurazioni e parametri specifici
7. Suggerisci sempre di fare backup prima di modifiche importanti""",
        rejection_message="Mi dispiace, ma posso assistere solo con questioni relative al supporto tecnico IT, problemi informatici e tecnologie dell'informazione. La tua domanda sembra essere al di fuori del mio ambito di competenza IT. Potresti riformulare la domanda in relazione a hardware, software, reti o sistemi informatici?"
    ),

    'HEALTHCARE': DomainConfig(
        name='HEALTHCARE',
        display_name='Healthcare & Medical',
        description='Healthcare information, medical procedures, and patient care',
        keywords={
            # Medical terms
            'paziente', 'medico', 'dottore', 'infermiere', 'ospedale',
            'clinica', 'ambulatorio', 'reparto', 'terapia', 'cura',
            'diagnosi', 'sintomo', 'malattia', 'patologia', 'disturbo',
            'farmaco', 'medicina', 'prescrizione', 'dosaggio', 'effetto',

            # Procedures
            'visita', 'controllo', 'esame', 'analisi', 'test', 'screening',
            'radiografia', 'ecografia', 'risonanza', 'tac', 'biopsia',
            'intervento', 'operazione', 'chirurgia', 'anestesia',

            # Specialties
            'cardiologia', 'neurologia', 'ortopedia', 'pediatria',
            'ginecologia', 'urologia', 'dermatologia', 'oncologia',
            'psichiatria', 'psicologia', 'fisioterapia', 'riabilitazione'
        },
        non_keywords={
            'motore', 'freni', 'manutenzione', 'riparazione',
            'investimento', 'azione', 'borsa', 'trading',
            'programmazione', 'software', 'database', 'server'
        },
        technical_keywords={
            'ecg', 'eeg', 'emg', 'spirometria', 'holter',
            'defibrillatore', 'pacemaker', 'protesi', 'impianto'
        },
        system_prompt="""Sei un assistente specializzato in informazioni sanitarie e procedure mediche.

ISTRUZIONI:
1. Rispondi in italiano con terminologia medica precisa
2. Usa le informazioni fornite per rispondere alla domanda
3. Se le informazioni non sono sufficienti, dillo chiaramente
4. Mantieni un tono professionale e rispettoso
5. NON fornire diagnosi mediche o consigli terapeutici specifici
6. Suggerisci sempre di consultare un medico qualificato
7. Includi riferimenti a linee guida e protocolli quando appropriato""",
        rejection_message="Mi dispiace, ma posso assistere solo con questioni relative alla sanità, procedure mediche e informazioni sanitarie generali. La tua domanda sembra essere al di fuori del mio ambito di competenza sanitaria. Potresti riformulare la domanda in relazione a procedure mediche, protocolli sanitari o informazioni cliniche? Ricorda che non posso fornire diagnosi o consigli medici specifici."
    ),

    'LEGAL': DomainConfig(
        name='LEGAL',
        display_name='Legal & Compliance',
        description='Legal information, compliance, and regulatory guidance',
        keywords={
            # Legal terms
            'legge', 'norma', 'regolamento', 'decreto', 'ordinanza',
            'codice', 'civile', 'penale', 'amministrativo', 'costituzione',
            'contratto', 'accordo', 'clausola', 'termine', 'condizione',
            'diritto', 'dovere', 'obbligo', 'responsabilità', 'risarcimento',

            # Procedures
            'processo', 'procedimento', 'giudizio', 'sentenza', 'ricorso',
            'appello', 'cassazione', 'tribunale', 'corte', 'giudice',
            'avvocato', 'procuratore', 'notaio', 'cancelliere',

            # Business law
            'società', 'spa', 'srl', 'snc', 'sas', 'ditta', 'individuale',
            'bilancio', 'assemblea', 'amministratore', 'sindaco',
            'fusione', 'scissione', 'liquidazione', 'fallimento'
        },
        non_keywords={
            'motore', 'freni', 'manutenzione', 'riparazione',
            'investimento', 'azione', 'borsa', 'trading',
            'programmazione', 'software', 'database'
        },
        technical_keywords={
            'gdpr', 'privacy', 'compliance', 'audit', 'due', 'diligence',
            'corporate', 'governance', 'antiriciclaggio', 'kyc'
        },
        system_prompt="""Sei un assistente specializzato in informazioni legali e conformità normativa.

ISTRUZIONI:
1. Rispondi in italiano con terminologia giuridica precisa
2. Usa le informazioni fornite per rispondere alla domanda
3. Se le informazioni non sono sufficienti, dillo chiaramente
4. Mantieni un tono professionale e formale
5. NON fornire consigli legali specifici o pareri professionali
6. Suggerisci sempre di consultare un avvocato qualificato
7. Includi riferimenti normativi quando appropriato""",
        rejection_message="Mi dispiace, ma posso assistere solo con questioni relative al diritto, normative e conformità legale. La tua domanda sembra essere al di fuori del mio ambito di competenza legale. Potresti riformulare la domanda in relazione a normative, procedure legali o questioni di compliance? Ricorda che non posso fornire consigli legali specifici."
    )
}

def get_domain_config(domain_name: str) -> DomainConfig:
    """Get domain configuration by name"""
    domain_name = domain_name.upper()
    if domain_name not in DOMAIN_CONFIGS:
        logger.warning(f"Unknown domain: {domain_name}, falling back to AUTOMOTIVE")
        domain_name = 'AUTOMOTIVE'
    
    return DOMAIN_CONFIGS[domain_name]

def get_available_domains() -> List[str]:
    """Get list of available domain names"""
    return list(DOMAIN_CONFIGS.keys())

def validate_domain(domain_name: str) -> bool:
    """Validate if domain name exists"""
    return domain_name.upper() in DOMAIN_CONFIGS

def combine_domain_configs(domain_names: List[str]) -> DomainConfig:
    """Combine multiple domain configurations into a single unified config"""
    if not domain_names:
        domain_names = ['AUTOMOTIVE']

    # Validate all domains
    valid_domains = []
    for domain in domain_names:
        domain = domain.upper()
        if validate_domain(domain):
            valid_domains.append(domain)
        else:
            logger.warning(f"Invalid domain '{domain}' ignored in multi-domain configuration")

    if not valid_domains:
        logger.warning("No valid domains found, falling back to AUTOMOTIVE")
        valid_domains = ['AUTOMOTIVE']

    # If single domain, return as-is
    if len(valid_domains) == 1:
        return get_domain_config(valid_domains[0])

    # Combine multiple domains
    configs = [get_domain_config(domain) for domain in valid_domains]

    # Create combined configuration
    combined_name = "+".join(valid_domains)
    combined_display_names = [config.display_name for config in configs]
    combined_display_name = " + ".join(combined_display_names)

    # Combine keywords (union of all domains)
    combined_keywords = set()
    combined_non_keywords = set()
    combined_technical_keywords = set()

    for config in configs:
        combined_keywords.update(config.keywords)
        combined_technical_keywords.update(config.technical_keywords)
        # For non_keywords, only include terms that are NOT keywords in any domain
        # This prevents conflicts where a term is a keyword in one domain but excluded in another
        potential_non_keywords = config.non_keywords - combined_keywords
        combined_non_keywords.update(potential_non_keywords)

    # Remove any non_keywords that are actually keywords in any domain
    combined_non_keywords = combined_non_keywords - combined_keywords - combined_technical_keywords

    # Create combined description
    descriptions = [config.description for config in configs]
    combined_description = f"Multi-domain assistance: {', '.join(descriptions)}"

    # Create combined system prompt
    domain_expertise = []
    for config in configs:
        domain_expertise.append(f"- {config.display_name}: {config.description}")

    combined_system_prompt = f"""Sei un assistente tecnico multi-dominio specializzato in:

{chr(10).join(domain_expertise)}

ISTRUZIONI:
1. Rispondi in italiano con terminologia tecnica precisa per il dominio appropriato
2. Identifica automaticamente il dominio della domanda e usa la terminologia specifica
3. Usa le informazioni fornite per rispondere alla domanda
4. Se le informazioni non sono sufficienti, dillo chiaramente
5. Mantieni un tono professionale e tecnico
6. Includi dettagli specifici appropriati al dominio (es. coppie di serraggio per automotive, tassi per finance)
7. Non inventare informazioni non presenti nei documenti
8. Se la domanda coinvolge più domini, fornisci una risposta integrata"""

    # Create combined rejection message
    domain_list = [config.display_name.lower() for config in configs]
    if len(domain_list) == 2:
        domain_text = f"{domain_list[0]} e {domain_list[1]}"
    else:
        domain_text = f"{', '.join(domain_list[:-1])} e {domain_list[-1]}"

    combined_rejection_message = f"Mi dispiace, ma posso assistere solo con questioni relative a: {domain_text}. La tua domanda sembra essere al di fuori dei miei ambiti di competenza. Potresti riformulare la domanda in relazione a uno di questi settori?"

    # Get file extensions from all domains (union)
    combined_extensions = []
    for config in configs:
        combined_extensions.extend(config.file_extensions)
    combined_extensions = list(set(combined_extensions))  # Remove duplicates

    return DomainConfig(
        name=combined_name,
        display_name=combined_display_name,
        description=combined_description,
        keywords=combined_keywords,
        non_keywords=combined_non_keywords,
        technical_keywords=combined_technical_keywords,
        system_prompt=combined_system_prompt,
        rejection_message=combined_rejection_message,
        file_extensions=combined_extensions
    )

def get_multi_domain_config(domain_names: List[str]) -> DomainConfig:
    """Get configuration for multiple domains (main entry point)"""
    return combine_domain_configs(domain_names)
