# 🔄 Guida alla Clonazione di Installazioni Multiple

Questa guida spiega come duplicare un'installazione esistente del **Softway Chatbot SYM** sullo stesso server senza causare conflitti.

## 📋 Prerequisiti

- Accesso SSH al server
- Installazione esistente funzionante (`installazione-A`)
- Permessi per creare nuove directory e database
- MySQL/MariaDB con privilegi per creare nuovi database

## 🎯 Panoramica del Processo

Il sistema supporta installazioni multiple attraverso:
- **INSTALLATION_ID**: Identificatore univoco per ogni installazione
- **Percorsi separati**: Cache, log e file temporanei isolati
- **Database separati**: Database con suffisso dell'installation ID
- **Porte diverse**: Ogni installazione usa una porta web diversa

## 🚀 Procedura Passo-Passo

### Passo 1: Clonazione del Codice

```bash
# Naviga nella directory parent delle installazioni
cd /path/to/installations

# Clona l'installazione esistente
cp -r installazione-A installazione-B

# Entra nella nuova installazione
cd installazione-B
```

### Passo 2: Pulizia dell'Installazione

Usa lo script di pulizia automatico per rimuovere tutti i dati dell'installazione precedente:

```bash
# Esegui pulizia completa (modalità dry-run per vedere cosa verrà fatto)
python clean_installation.py --dry-run

# Esegui pulizia effettiva
python clean_installation.py --force

# Oppure pulizia interattiva (con conferme)
python clean_installation.py
```

Lo script pulirà automaticamente:
- ✅ Cache dell'applicazione
- ✅ File di log
- ✅ File temporanei e upload
- ✅ Cache delle whitelist
- ✅ Sessioni attive
- ✅ Backup del file .env esistente
- ✅ Creazione di un nuovo .env dal template

### Passo 3: Configurazione della Nuova Installazione

Modifica il file `.env` con le impostazioni specifiche per la nuova installazione:

```bash
nano .env
```

**Configurazioni OBBLIGATORIE da modificare:**

```env
# Identificatore univoco per questa installazione
INSTALLATION_ID=installazione-b

# Porta web diversa per evitare conflitti
WEB_PORT=8001

# Database separato
DB_DATABASE=softway_chat_installazione_b

# Percorsi (opzionale - vengono gestiti automaticamente)
CACHE_PATH=./cache_installazione_b
LOG_PATH=./logs_installazione_b

# Redis DB diverso (se usi Redis)
REDIS_DB=1

# Chiave API segreta univoca
API_SECRET_KEY=your_unique_secret_key_for_installation_b
```

**Esempio di configurazioni per installazioni multiple:**

```env
# INSTALLAZIONE A (Produzione)
INSTALLATION_ID=prod
WEB_PORT=8000
DB_DATABASE=softway_chat_prod
REDIS_DB=0

# INSTALLAZIONE B (Staging)
INSTALLATION_ID=staging
WEB_PORT=8001
DB_DATABASE=softway_chat_staging
REDIS_DB=1

# INSTALLAZIONE C (Sviluppo)
INSTALLATION_ID=dev
WEB_PORT=8002
DB_DATABASE=softway_chat_dev
REDIS_DB=2
```

### Passo 4: Setup del Database

Crea il database per la nuova installazione:

```bash
# Metodo automatico (raccomandato)
python setup_database.py

# Metodo manuale
mysql -u root -p
CREATE DATABASE softway_chat_installazione_b;
GRANT ALL PRIVILEGES ON softway_chat_installazione_b.* TO 'prova'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Importa lo schema
mysql -u prova -p softway_chat_installazione_b < database/create_chatbot_logs_table.sql
```

### Passo 5: Installazione delle Dipendenze

```bash
# Installa/aggiorna le dipendenze Python
python setup.py

# Oppure manualmente
pip install -r requirements.txt
```

### Passo 6: Test della Nuova Installazione

```bash
# Test configurazione
python -c "from config import config; print(f'Installation ID: {config.installation_id}')"

# Test database
python -c "from database_logger import get_database_logger; import asyncio; asyncio.run(get_database_logger())"

# Avvia l'interfaccia web
python start_web.py

# Test CLI
python main.py
```

### Passo 7: Verifica dei Conflitti

Controlla che non ci siano conflitti:

```bash
# Verifica porte in uso
netstat -tlnp | grep :8000
netstat -tlnp | grep :8001

# Verifica database
mysql -u prova -p -e "SHOW DATABASES;" | grep softway_chat

# Verifica percorsi
ls -la cache*
ls -la logs*
```

## 🔧 Configurazioni Avanzate

### Reverse Proxy (Nginx)

Se usi un reverse proxy, configura vhost separati:

```nginx
# /etc/nginx/sites-available/chatbot-prod
server {
    listen 80;
    server_name chatbot-prod.example.com;
    location / {
        proxy_pass http://localhost:8000;
    }
}

# /etc/nginx/sites-available/chatbot-staging
server {
    listen 80;
    server_name chatbot-staging.example.com;
    location / {
        proxy_pass http://localhost:8001;
    }
}
```

### Systemd Services

Crea servizi separati per ogni installazione:

```ini
# /etc/systemd/system/chatbot-prod.service
[Unit]
Description=Softway Chatbot SYM - Production
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/installazione-A
ExecStart=/path/to/installazione-A/env/bin/python start_web.py
Restart=always

[Install]
WantedBy=multi-user.target

# /etc/systemd/system/chatbot-staging.service
[Unit]
Description=Softway Chatbot SYM - Staging
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/installazione-B
ExecStart=/path/to/installazione-B/env/bin/python start_web.py
Restart=always

[Install]
WantedBy=multi-user.target
```

Attiva i servizi:

```bash
sudo systemctl enable chatbot-prod
sudo systemctl enable chatbot-staging
sudo systemctl start chatbot-prod
sudo systemctl start chatbot-staging
```

## 🛠️ Manutenzione

### Aggiornamento di Tutte le Installazioni

```bash
# Script per aggiornare tutte le installazioni
for installation in installazione-A installazione-B installazione-C; do
    echo "Updating $installation..."
    cd /path/to/$installation
    git pull origin main
    pip install -r requirements.txt
    sudo systemctl restart chatbot-${installation#installazione-}
done
```

### Backup Automatico

```bash
# Script di backup per tutte le installazioni
#!/bin/bash
BACKUP_DIR="/backups/chatbot/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

for installation in installazione-A installazione-B; do
    # Backup database
    mysqldump -u prova -p softway_chat_${installation#installazione-} > $BACKUP_DIR/${installation}_db.sql
    
    # Backup configurazione
    cp /path/to/$installation/.env $BACKUP_DIR/${installation}_env
    
    # Backup logs importanti
    tar -czf $BACKUP_DIR/${installation}_logs.tar.gz /path/to/$installation/logs*
done
```

## ⚠️ Problemi Comuni e Soluzioni

### Conflitto di Porte
**Problema**: `Address already in use`
**Soluzione**: Verifica che ogni installazione usi una porta diversa in `WEB_PORT`

### Database già esistente
**Problema**: `Database already exists`
**Soluzione**: Usa un nome database diverso o elimina il database esistente

### Percorsi condivisi
**Problema**: Le installazioni condividono cache/log
**Soluzione**: Verifica che `INSTALLATION_ID` sia diverso per ogni installazione

### Permessi file
**Problema**: Errori di permessi su file/directory
**Soluzione**: 
```bash
sudo chown -R www-data:www-data /path/to/installazione-B
sudo chmod -R 755 /path/to/installazione-B
```

## 📞 Supporto

Per problemi specifici:
1. Controlla i log: `tail -f logs_[installation_id]/chatbot.log`
2. Verifica la configurazione: `python -c "from config import config; print(vars(config))"`
3. Testa la connessione database: `python test_database_integration.py`

---

**✅ Seguendo questa guida, dovresti avere installazioni multiple completamente isolate e funzionanti!**
