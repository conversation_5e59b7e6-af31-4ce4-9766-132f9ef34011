# 🏗️ Sistema Multi-Installazione - Documentazione Tecnica

Questo documento descrive l'architettura e l'implementazione del sistema multi-installazione per **Softway Chatbot SYM**.

## 🎯 Obiettivi Raggiunti

✅ **Configurazione Centralizzata**: Tutta la configurazione variabile gestita tramite `.env`  
✅ **Isolamento Completo**: Ogni installazione ha database, cache, log e porte separate  
✅ **Utility di Pulizia**: Script automatico per preparare nuove installazioni  
✅ **Gestione Semplificata**: Tool CLI per creare e gestire installazioni multiple  
✅ **Guida Dettagliata**: Documentazione completa per la clonazione manuale  

## 🏛️ Architettura del Sistema

### Identificazione delle Installazioni

Ogni installazione è identificata univocamente da:
- **INSTALLATION_ID**: Identificatore univoco (es: `prod`, `staging`, `dev`)
- **Percorsi Isolati**: Cache, log e file temporanei con suffisso dell'ID
- **Database Separato**: Nome database con suffisso dell'installation ID
- **Porta Dedicata**: Ogni installazione usa una porta web diversa

### Struttura dei File

```
/server/
├── installazione-A/                 # Installazione Produzione
│   ├── .env                        # INSTALLATION_ID=prod, WEB_PORT=8000
│   ├── cache_prod/                 # Cache isolata
│   ├── logs_prod/                  # Log isolati
│   └── [codice applicazione]
├── installazione-B/                 # Installazione Staging  
│   ├── .env                        # INSTALLATION_ID=staging, WEB_PORT=8001
│   ├── cache_staging/              # Cache isolata
│   ├── logs_staging/               # Log isolati
│   └── [codice applicazione]
└── installazione-C/                 # Installazione Development
    ├── .env                        # INSTALLATION_ID=dev, WEB_PORT=8002
    ├── cache_dev/                  # Cache isolata
    ├── logs_dev/                   # Log isolati
    └── [codice applicazione]
```

### Database Multi-Installazione

```sql
-- Database separati per ogni installazione
softway_chat_prod     -- Produzione
softway_chat_staging  -- Staging  
softway_chat_dev      -- Development
```

## 🔧 Componenti Implementati

### 1. Configurazione Avanzata (`config.py`)

**Nuove Funzionalità:**
- Supporto per `INSTALLATION_ID` in tutti i percorsi
- Database automaticamente prefissato con installation ID
- Configurazione Redis con offset per installation ID
- Gestione porte e host configurabili
- Configurazioni di sicurezza e sessione

**Esempio di Utilizzo:**
```python
from config import config

# Automaticamente risolve percorsi con installation ID
cache_path = config.cache_path        # ./cache_prod (se INSTALLATION_ID=prod)
db_name = config.db_database          # softway_chat_prod
web_port = config.web_port            # 8000 (configurabile)
```

### 2. Script di Pulizia (`clean_installation.py`)

**Funzionalità:**
- ✅ Pulizia cache applicazione
- ✅ Rimozione file di log
- ✅ Pulizia file temporanei e upload
- ✅ Reset cache whitelist
- ✅ Eliminazione sessioni attive
- ✅ Backup automatico del `.env` esistente
- ✅ Creazione nuovo `.env` da template
- ✅ Modalità dry-run per preview
- ✅ Modalità force per automazione

**Utilizzo:**
```bash
# Preview delle operazioni
python clean_installation.py --dry-run

# Pulizia completa interattiva
python clean_installation.py

# Pulizia automatica (per script)
python clean_installation.py --force

# Mantieni configurazione esistente
python clean_installation.py --keep-config
```

### 3. Gestore Installazioni (`manage_installations.py`)

**Funzionalità:**
- ✅ Creazione automatica di nuove installazioni
- ✅ Lista e stato di tutte le installazioni
- ✅ Rimozione sicura di installazioni
- ✅ Assegnazione automatica porte disponibili
- ✅ Copia intelligente del codice (esclude dati)
- ✅ Generazione automatica file `.env`

**Utilizzo:**
```bash
# Lista installazioni
python manage_installations.py list

# Stato di tutte le installazioni
python manage_installations.py status

# Crea nuova installazione
python manage_installations.py create staging --port 8001

# Rimuovi installazione
python manage_installations.py remove staging --force
```

### 4. Configurazione Template (`.env.example`)

**Sezioni Organizzate:**
- 🏷️ **Installation Configuration**: ID, nome, versione, ambiente
- 🌐 **Network Configuration**: Host, porte, worker
- 🔑 **API Configuration**: Chiavi API e modelli
- 📁 **Paths Configuration**: Percorsi con supporto installation ID
- 🗄️ **Database Configuration**: Database con prefisso automatico
- ⚡ **Performance Settings**: Ottimizzazioni e cache
- 🔒 **Security Configuration**: Sicurezza e rate limiting
- 🧹 **Cleanup Configuration**: Pulizia automatica

### 5. Guida Clonazione (`CLONING_GUIDE.md`)

**Contenuti:**
- 📋 Prerequisiti e panoramica
- 🚀 Procedura passo-passo dettagliata
- 🔧 Configurazioni avanzate (Nginx, Systemd)
- 🛠️ Script di manutenzione
- ⚠️ Risoluzione problemi comuni

## 🚀 Flusso di Lavoro Tipico

### Scenario 1: Creazione Rapida Installazione

```bash
# 1. Crea nuova installazione
python manage_installations.py create staging --port 8001

# 2. Configura installazione
cd installation_staging
nano .env  # Modifica configurazioni specifiche

# 3. Setup database
python setup_database.py

# 4. Avvia servizio
python start_web.py
```

### Scenario 2: Clonazione Manuale Dettagliata

```bash
# 1. Clona directory
cp -r installazione-A installazione-B

# 2. Pulisci installazione
cd installazione-B
python clean_installation.py --force

# 3. Configura .env
nano .env
# INSTALLATION_ID=staging
# WEB_PORT=8001
# DB_DATABASE=softway_chat_staging

# 4. Setup e avvio
python setup_database.py
python start_web.py
```

## 🔍 Vantaggi del Sistema

### Per gli Sviluppatori
- **Isolamento Completo**: Nessun conflitto tra installazioni
- **Setup Rapido**: Creazione nuove installazioni in minuti
- **Configurazione Semplice**: Un solo file `.env` da modificare
- **Debugging Facilitato**: Log e cache separati per installazione

### Per DevOps
- **Automazione**: Script per creazione e pulizia automatica
- **Monitoraggio**: Stato di tutte le installazioni in un comando
- **Scalabilità**: Facile aggiunta di nuove installazioni
- **Manutenzione**: Update e backup semplificati

### Per la Produzione
- **Stabilità**: Installazioni isolate non si influenzano
- **Testing**: Staging e development separati dalla produzione
- **Rollback**: Possibilità di mantenere versioni multiple
- **Performance**: Ogni installazione ottimizzata indipendentemente

## 📊 Metriche e Monitoraggio

### Database Separati
Ogni installazione ha il proprio database con schema completo:
- Conversazioni isolate per ambiente
- Analytics separate per installazione
- Backup indipendenti

### Log Strutturati
```
logs_prod/chatbot.log      # Log produzione
logs_staging/chatbot.log   # Log staging
logs_dev/chatbot.log       # Log development
```

### Cache Isolate
```
cache_prod/               # Cache produzione
cache_staging/            # Cache staging  
cache_dev/                # Cache development
```

## 🔒 Sicurezza Multi-Installazione

### Isolamento dei Dati
- ✅ Database separati per installazione
- ✅ Cache isolate per installazione
- ✅ Log separati per installazione
- ✅ Sessioni isolate per installazione

### Configurazioni di Sicurezza
- ✅ Chiavi API separate per installazione
- ✅ Rate limiting indipendente
- ✅ CORS configurabile per installazione
- ✅ Host consentiti configurabili

## 🛠️ Manutenzione e Aggiornamenti

### Update di Tutte le Installazioni
```bash
#!/bin/bash
for installation in prod staging dev; do
    echo "Updating installation: $installation"
    cd /path/to/installation_$installation
    git pull origin main
    pip install -r requirements.txt
    # Restart service if needed
done
```

### Backup Automatico
```bash
#!/bin/bash
for installation in prod staging dev; do
    # Backup database
    mysqldump softway_chat_$installation > backup_${installation}_$(date +%Y%m%d).sql
    
    # Backup configuration
    cp installation_$installation/.env backup_env_${installation}_$(date +%Y%m%d)
done
```

## 📞 Supporto e Troubleshooting

### Comandi di Diagnostica
```bash
# Verifica configurazione
python -c "from config import config; print(f'ID: {config.installation_id}, DB: {config.db_database}, Port: {config.web_port}')"

# Verifica database
python -c "from database_logger import get_database_logger; import asyncio; asyncio.run(get_database_logger())"

# Verifica porte in uso
netstat -tlnp | grep :800

# Lista installazioni
python manage_installations.py list
```

### Log di Sistema
Ogni installazione mantiene log separati in `logs_[installation_id]/chatbot.log`

---

## 🎉 Conclusioni

Il sistema multi-installazione implementato fornisce:

✅ **Isolamento Completo** tra installazioni  
✅ **Gestione Semplificata** tramite utility CLI  
✅ **Configurazione Centralizzata** tramite `.env`  
✅ **Automazione Completa** per setup e pulizia  
✅ **Documentazione Dettagliata** per tutti i casi d'uso  

Il sistema è ora pronto per essere clonato e deployato in ambienti multipli senza conflitti, con pieno supporto per produzione, staging e development sullo stesso server.
