{"whitelist": ["datotarga 198", "versione 1", "param 223", "riservati 09", "fuel2", "bancadatiservicesoap12", "ethodresult 148", "arrayofpneumatico 123", "informazioni", "testuale", "ebmethodparam 167", "trovato 4", "da 1", "scorewebmethodresult 300", "s0302", "s04ur", "sistema 4", "destinazione", "sw 322", "documenti", "psa 368", "s0417", "consumokg1stimato", "m 134", "<PERSON><PERSON><PERSON><PERSON>", "equipaggiamento 211", "con 1", "android 7", "voci 312", "getquotazionenotequalificanti 70", "dative<PERSON>lo 170", "integrazioni 4", "porte", "cilindrata", "proveniente", "json 362", "fiscale", "batteria al", "modelliwebmethodresult 253", "interni", "j4p", "143cv f", "infocarweb 3", "manuale", "studio 319", "getcodicipubblicazione 18", "page 3", "rotazione", "scelta", "co2combinatominimo", "1gb", "marca", "ram 204", "o2dmstimata", "grafica 06", "datoallestimento 175", "consumo3", "ethodresult 290", "immatricolazioe", "consigliato 2gb", "panda 1", "batteria tra", "specializzati 15", "arrayofveicolort 126", "s03at", "ultimi 12", "ordine 269", "attenzione 000", "a 15", "workflow 1", "da<PERSON>in<PERSON><PERSON><PERSON><PERSON><PERSON> 186", "interfaccia 9", "modifica 13", "datodatitecnicidettagliati 182", "69cv gpl", "1560cc", "tergicristalli 5266", "odresult 295", "in 4", "freni", "bmet<PERSON>dparam 144", "calcoloprezzoacquisto 131", "ult 239", "pneumatico 272", "clio 1", "wltp", "arrayofnotacarrozzeria 121", "capacitabagagliaio1d", "preventivi 59", "s 0417", "rest 347", "gestione", "icante 117", "sult 251", "s0490", "usato", "capacitabagagliaio2d", "fd111aa", "arrayofdatomodellocompleto 113", "reale", "elettrico", "chiamata 2", "servoassistita 3947", "studio 1", "str04", "codificaas24", "co2combinato", "arrayofricambiotagliando 124", "10edizionistoriche", "o 118", "x 768", "30min", "result 243", "applicazione 5", "ibride 6", "filtro che", "a 20", "s06ak", "geticonespie 38", "wltp 366", "completa", "aziendali 3", "cf6", "codicicasawebmethodparam 139", "finanziamento", "wagon 4", "batteria del", "manuale infocarweb", "4xa", "venire", "arrayofdatiwltpmotore 109", "getmodelli 1", "portal 2", "fuel1", "result 223", "capacitabagagliao3dmstimata", "veicolo 21", "immagine", "annuncio", "lounge 1", "configurazione 31", "output 0", "uso", "ce 93", "s0610", "arrayofconsumi 108", "start 318", "php 327", "alimentazione", "euro 5", "indicato 0", "u92", "tipologierichiestecusomtercare", "trasporto", "da 5", "ethodparam 209", "ster<PERSON> 2731", "emissione1stimato", "accellerazione0100", "carrozzeria 374", "nuovo 5", "figura 4", "codicierrorewebmethodresult 140", "ione 116", "arrayofpianomanutenzione 122", "aa000000", "datodanno 182", "precedente 01", "co2wltp", "dispositivo 4", "crm 1", "ultimiaggiornamenti 303", "datoimmaginirepertorio 184", "2ew", "veicolo 8", "s0493", "chiusa 8", "uc06", "almeno 5", "nato 107", "acustico 193", "s0494", "entro 48", "giri", "modello 2404", "consigliata 4gb", "n1d", "getcodiciinfocareliminati 17", "s07le", "tab 4", "emissionico2", "quotazione", "oewau14fs", "stock 4", "equipaggiamentoqualificante 221", "stock 3", "4mbps in", "carbonica 3", "salva 5", "vgi 371", "i 260", "accelerazione0100", "120d ber", "management", "datocodicicasa 181", "datocasa 180", "<PERSON><PERSON><PERSON>", "con 6", "vis12", "olio freni", "monitoring", "e46", "ser62", "arrayofdatoallestimentotarga 110", "corpo 16", "reader 9", "testo 4", "registrazione", "o 116", "arrayofprevisioneusato 123", "thod<PERSON>ult 206", "co2highstimato", "e 100", "richiesta 17", "utenti 58", "tipologie", "x000", "trasmissione", "s05da", "sistema quattroruote", "categorie 47", "esult 202", "automatici 2", "8ysbdcwde", "2wd active", "arrayofdatocodicicasa 111", "cee 80", "gestionali", "codice 33671", "ore 9", "valori 1", "prime 2", "getconfigurazionedavin 23", "colore", "metodo 250", "sic02", "di 5", "equipaggiamentolistino 215", "quella", "d<PERSON><PERSON> 229", "siste<PERSON> terzi", "impianto relativo", "getscore 81", "arrayofordine 122", "result 245", "sic14", "criteriricerca 159", "s01df", "consumokg2", "modifica 08", "sistemi di", "il 4", "consumireport 153", "variante", "sistema viene", "anagraficawebmethodresult 106", "immatricolazione", "posizionamento", "di 2", "terzo", "param 245", "t 134", "infocar", "congeniale", "emissioneco2", "informazioni 234", "grafica 27", "<PERSON><PERSON><PERSON>", "cm3", "lega 5008", "giac<PERSON>za", "odresult 289", "aram 304", "preventivazione", "volumi 3", "marchevinwebmethodresult 246", "possibili 0", "getpreventivomeccanica 60", "relativo", "90cv", "getmarcheconfigurazionevin 53", "odparam 295", "veicoloannuncio 306", "256kbps in", "sicurezza 5394", "univoca", "solitamente 1", "tra 2", "batteria che", "getconfigurazionedatarga 21", "trattativa 4", "bmw 369", "utente 45", "driver", "per 30", "filtro tutte", "strada", "radiatore 3824", "di 14", "servi<PERSON> 400", "s02pa", "datolistinoallestimento 187", "competenza", "webmethodparam 284", "<PERSON><PERSON><PERSON>", "plus 1", "filtro categoria", "allestimento", "k55112789ac", "odresult 276", "versionewebmethodresult 310", "filtro viene", "categorie", "veicolo 48", "motore 2311", "le 5", "3b manuale", "massima", "xml 349", "mnv 5p", "s06af", "equipaggiamenti", "equipaggiamentoinclusione 213", "getpreventivotagliando 62", "consumo1stimato", "autonomiawltp 129", "firefox 56", "wsdl 3", "agliaio3dm", "d<PERSON><PERSON> 277", "consumo2", "tecniche", "uc04", "sommario 1", "zona 1", "auto 18", "fiscali 3", "trattativa 3", "avvenire", "i 15", "team", "json 363", "aram 172", "equipaggiamentipacchetto 207", "foto", "sistemi gestionali", "presentate 2", "uc07", "c36", "analisi", "previsionenuovo 279", "emissione3", "accessori", "ma 1", "accelerazione0100sti", "json 357", "j4d", "peso 50", "aliquote 4", "volumi 2", "x86", "demo 5", "sic01", "totalmente 9", "di 30", "omologazione", "bagagliao1dm", "capacitabagagliao1dm", "getquotazionestorica 79", "datoinfocar 184", "si<PERSON><PERSON> dalla", "https 3", "firewall", "d<PERSON><PERSON> 143", "sistema operativo", "sistema chiamante", "ram 231", "fornitura 2", "circolazione", "nucleomotore 267", "1299cc", "berlina 3v", "e 06", "a 6", "s05ac", "sistema manuale", "ottobre 2017", "veicolort 308", "120gg di", "importanti 1", "ultimi 11", "voce 312", "avviamento 277", "s04aw", "84kw", "studio 2010", "valido 60", "config 8", "da 2", "aria 2721", "correttamente 1", "manuale 384", "veicolosimilert 309", "4wd", "s02vb", "sistema 5", "config 4", "fca 367", "capacitabagagliaio2dm", "stessi 04", "tipologia", "ios 11", "getdizionariecodici 28", "2ait", "webmethodresult 314", "venditore", "filtro impostato", "infocar2", "altro 6", "bollo", "o 0", "bmethodresult 145", "passata9", "s0775", "s06ae", "api 4", "veicolo 19", "categorie 39", "internet 3", "tipo", "co2high", "aaa00000", "te<PERSON>o", "arrayofproperty 124", "s05az", "datoquotazionestorica 197", "interfaccia 2", "attuale", "45kw", "mhka9ak", "notameccanica 267", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>ag<PERSON><PERSON> 299", "getinterrogazionireport 48", "po 4562", "bmethodparam 291", "zata 64", "presenti 3", "b2v", "copyright 2017", "consumokg1", "ricerca 2", "disposizione 1", "univoco", "colonna", "alle 12", "sistema terzo", "errore 001", "getpreventivocarrozzeria 58", "qps1", "rtservicesoap12", "assistenza 3", "5cb", "emissione3stimato", "anagraficawebmethodparam 106", "esito 224", "cuscinetto 1813", "datiwltp 172", "gdpr 4", "copyright 2015", "altro 3", "alternativa 1", "320d td", "tra 1", "fornite 4", "filtro antipolline", "nuovo", "datiwltpmotore 174", "<PERSON><PERSON> 13", "datoquotazionestandard 196", "arrayofvoci 128", "1e manuale", "zzata 191", "peso 30", "importo", "ac<PERSON>o", "finale", "co2low", "tetto", "aria 586", "costo", "client 324", "veicolo 4", "1773cc", "euro 6", "capacitabagagliaio1dm", "filtro in", "a 1", "arrayofvoce 127", "quotazione 4r", "emissione2", "marca 8", "pneumatici 1", "a 90", "cb7", "getmarchewltpdinamici 54", "risultati 42", "modello 85", "commerciale 2", "methodresult 282", "e 13", "infocarweb 4", "applicazione 1", "altri 10", "getlistinoallestimento 51", "caratteristiche 133", "diverso 4", "getmodellicompleti 56", "zfa31200003600000", "s01ub", "property 287", "carico", "posti", "elettroventola 862", "azioni 77", "istanza", "mese 1997", "co2combina<PERSON><PERSON><PERSON>", "vis02", "elettriche 8", "4a manuale", "risposta", "mnav7ug", "px2", "aperta 7", "xx001yy", "username", "operativo", "alizzato 117", "soap 328", "m 238", "windows 7", "00n", "aj19agabcimovz", "massimo 5", "berlina 2v", "64kbps in", "<PERSON><PERSON><PERSON> 278", "durante", "dresult 241", "arrayoficonaspiafile 119", "dalla", "z61", "arrayofdatolistinoallestimento112", "motore", "a 3", "getallestimenti 11", "sistema fatturazione", "getecho 29", "copertura 2", "mercato", "opzione", "arrayofdatocodicierrore 111", "arrayofintervallotagliando 119", "autonomia", "o 1", "sistema elettrico", "compressore 5595", "cliente 1", "getquotazionecertificata 68", "framework 4", "consultazioni 0", "d<PERSON><PERSON> 228", "passeg 4143", "getultimiequipcasa 85", "in 6", "vendita", "libero 1", "altezza", "in 3", "textbox1", "<PERSON><PERSON><PERSON><PERSON> 188", "abbreviazioni 10", "getprevisioneusatostandard 67", "038131521cb", "veicolo 305", "crm 3", "applicazione 10", "sistema non", "codifica 146", "listini", "anche 0", "sw 5p", "consumi 151", "codice", "s05dc", "motoreprestazioni 254", "getcodicierrore 16", "ebmethodresult 167", "immagini", "metodo 27", "configurazione", "euro 4", "febbraio 2025", "servizio 5", "proven<PERSON>za", "desiderato 1", "sione 115", "6hq", "operativi", "sult 302", "consegna", "cancellazione", "t1da", "datoversione 201", "registrazione 6", "serie 3", "marchewebmethodresult 248", "consumo1", "valore", "veicolo 6", "json 359", "methodparam 286", "100km", "il 20", "tdi 143cv", "getintervallitagliando 50", "ico10", "almeno 3", "nformazioni 73", "getequipaggiamentiregole 32", "datowltpdinamico 201", "arrayofdatomodello 113", "consumokg3", "corrispondente 1", "div54", "stato 3", "s0255", "nuovo 3", "win64", "trimestrale 1", "s08tf", "param 296", "critericomparazione 158", "co2mediumstimato", "aggiornata 2", "produzione", "cili<PERSON><PERSON>", "odparam 141", "consumokg3stimato", "sistema e", "esult 173", "ordinaria 377", "listino", "interno", "cb4", "infocarweb", "scheda", "sistema ibrido", "1995cc", "aa00000", "datoquotazionepersonalizzata 195", "cambio", "webmethodparam 313", "co2combinatominim", "dresult 143", "generale 4", "descrizione", "textbox2", "delle 3", "monovolume 5", "silenziator 2343", "utf8", "vol78", "arrayofcodifica 108", "getmodelli 55", "explorer 11", "gasolio 3", "5al", "eg026nx", "presenti 4", "fleet 1", "autonomie 130", "tendina 4617", "colori 55", "quotazioni 600", "odresult 142", "stato", "consigliata 512", "da<PERSON><PERSON><PERSON><PERSON> 170", "s0431", "datoprevisioninuovostandard 193", "a 60", "categoria 39", "<PERSON>ia rispetto", "uc14", "notacarrozzeria 266", "it 4", "ritiro 4", "colori 23", "bsn1d", "am 230", "sezione 2", "iconaspiafile 227", "2a manuale", "indice 1", "arrayofdatoequipaggiamento 112", "tecnica 2", "datocodicierrore 181", "xml 354", "qualificante", "agenda", "webmethodparam 225", "datitecnicibase 161", "datoallestiment<PERSON><PERSON><PERSON> 178", "co2lowstimato", "scomparsa 17", "over 120", "differenti", "ult 270", "nome", "iw3", "getversioneservizio 86", "manuale infocarfleet", "arrayofmodulo 120", "arrayofveicolosimilert 127", "cicliwltphybrid 136", "s01cb", "co2", "valore 31", "038130073bj", "dn383ef", "quattroruote 1", "categorie 38", "peso 20", "utenti 42", "sistema durante", "chiamante", "ibrido", "hetto 117", "inser 3", "17x", "equipaggiamento", "chiamata 3", "vinprovider 311", "4gb", "metodo 3", "emissione2stimato", "mese 1993", "getsvgcarrozzeria 83", "zzata 190", "cilindro", "j4k", "e 6", "s1ft", "sistema per", "sistema di", "ritiro", "revisione", "chilometri", "sintesi", "pianomanutenzione 271", "documentazione", "7ak", "arrayofprevisionenuovo 123", "datomodellocompleto 189", "datoequipaggiamento 183", "presentazione", "massimo 60", "methodparam 293", "applicazione 2", "pr 1794", "concessionaria", "con 3", "odre<PERSON>t 249", "stabile 3", "modifica 11", "s0654", "emissione1", "mp3", "chilometraggio", "ethodresult 210", "arrayofdatoversione 114", "mcpait4", "disponibile 0", "cat 3", "gpl 5m", "getinfocardatarga 41", "getedizionistoriche 30", "faq", "s<PERSON><PERSON> 3440", "methodresult 294", "getmarche 52", "del 15", "del 2015", "sistemi operativi", "incapacitabagagliaio1dm", "anagrafica", "consumo3stimato", "getquotazionestandard 75", "anagraficainfocareliminato 105", "codifica", "012300045bx", "param 242", "da 4", "awebmethodparam 280", "s0548", "json 03", "an 528", "s0884", "immatr54icolazione", "getprevisionenuovostandard 65", "ram 235", "consumicarburante 152", "fatturazione", "<PERSON><PERSON><PERSON>", "fiscali", "bs274yj", "instantwebdata 234", "arrayofnotameccanica 122", "61a", "3c manuale", "getdatiwltpdinamici 26", "sistema gestionale", "inserire 0", "selezione", "vai 4", "sportage 1", "dizionario 203", "equipaggiamentovincolo 221", "co2combinatostimat", "riparazione", "del 10", "edge 16", "oqualificante 108", "sult 231", "consigliato 512", "allestimentiwebmethodresult 104", "codici 1", "sistema si", "s000", "webmethodresult 225", "arrayofdizionario 114", "i 10", "viene", "destinatario 2", "esterno", "studio 2005", "serrature 2888", "parametri 50", "json 360", "soap12", "gagliao1dmstimata", "allestimentiwebmethodparam 103", "infocarfleet", "identificativo", "datoallestimentotarga 177", "etto 114", "a 10", "s0230", "batteria", "esempio 2", "straordinaria 375", "paragrafo 4", "s0430", "arrayofesito 118", "ricerca", "sult 237", "venditore 9", "rilascio 1", "2c manuale", "parametri 500", "cante 138", "alle 17", "entro 24", "codice 4", "intervallotagliando 244", "odparam 275", "di 10", "passo 4", "equipaggiamentoesclusione 212", "am 270", "filtro olio", "<PERSON><PERSON><PERSON><PERSON><PERSON> 1797", "richiesta 03", "versione 04", "s0962", "s04ne", "aggiornamento 11", "privati", "totalmente 10", "arrayofequipaggiamento 115", "tecnici", "venisse", "quattroruote", "manutenzione", "stock 1", "benzina 2", "lux66", "specifica 2", "datoinfocardatarga 185", "api 3", "oscillan 3426", "s05as", "finanziamento 1", "arrayofdatocasa 110", "bmethodresult 292", "getanagrafica 13", "ami 36", "ultimi 15", "rtwebservices", "getequipnormalizzati 34", "funzionamento", "dimensioni", "<PERSON><PERSON><PERSON>", "al 02", "accesso 3", "da<PERSON> 4", "sult 204", "getbancadatiservicesoap12", "sult 232", "s0508", "sistema lrw", "datoprevisioniusatostandard 194", "step 301", "inserimento", "odparam 149", "loro 2", "json 356", "codicicasawebmethodresult 139", "methodresult 286", "arrayofveicoloannuncio 125", "iva", "ethodparam 290", "risultati 60", "p 24", "sed02", "equipaggiamentopacchetto 220", "destinatario", "del 99", "inseriti 2", "assistenza 4", "temporale", "proporzionalmente", "s0534", "web", "metano 4", "domuscredential 203", "login 11", "p0wp", "hodparam 273", "metodo 2", "wltpprovider 316", "datoultimiequipcasa 200", "arrayofmetodo 120", "44kw", "policy 57", "utenti 41", "a 517", "radiatore 2383", "geo 226", "s 0494", "massimo 3", "xml 352", "email 3", "<PERSON><PERSON><PERSON><PERSON>", "appartenenza", "vapore", "list 49", "terzi", "figura 1", "etanolo 7", "estero", "s0205", "xml 355", "veicolotagliando 309", "added 51", "getinfocardatelaio 43", "j4m", "preselezione", "marchevinwebmethodparam 246", "chilowatt", "services 1", "75 kg", "cl109na", "identificative", "co2extrahighstimat", "studio 10", "arrayofwltpprovider 128", "json 364", "consumokg2stimato", "c000", "veicolodavin 307", "browser 2", "caratteristiche", "permessi 2", "di 1", "avant 2", "sistema risoluzione", "tergiparabrezza 3783", "2gb", "getinterrogazionitarga 49", "as24", "eg031nx", "vol86", "accelerazione0100stimata", "potenza", "marchewebmethodparam 247", "metodi 6", "datitecniciibride 168", "prestazioni", "uc03", "vendita 1", "ltp2", "il 06", "j4n", "sistema dei", "elettrico 1", "radiatore 3823", "1951cc", "la 3", "incapacitabagagliaio3dmstimata", "mese 1994", "7le", "online", "airbag 3815", "massimo 100", "targhe 101", "stabi 3162", "a 12", "provenienti", "sterzo 3503", "o 112", "sezioni 11", "presenti 2", "previsioneusato 283", "jtdm 20v", "capacitabagagliao2dmstimata", "categoria 40", "giornirotazione 227", "licenza", "di 100", "od<PERSON><PERSON> 288", "j4s", "beni", "immatricolazione 7", "methodparam 281", "corpodimensioni 155", "presenza", "individuazione", "filtro archivio", "arrayofsvgcarrozzeriafile 125", "698cc", "risultati 34", "capacitabagagliaio3dm", "getimmaginirepertorio 40", "page 12", "<PERSON>ia questa", "sic18", "infocarweb 2", "yd07", "calcoloprezzovendita 132", "esult 304", "per 365", "grafica 02", "talvolta 7", "note 1", "str20", "arrayofstep 125", "massimo 14", "identificazione", "partita", "webmethodresult 285", "pastello 2", "cicliwltp 135", "i 2", "categoria", "sconto 1", "s0676", "uc13", "arrayofvinprovider 127", "infocarweb 1", "<PERSON><PERSON><PERSON><PERSON>", "copyright 2014", "sezioni 3", "5by", "<PERSON><PERSON><PERSON><PERSON>", "capacitabagagliao1dmstimata", "immatricolazione 3", "otten<PERSON>le", "dresult 228", "chrome 61altro", "prezzo", "opzionali 5", "servizio 105", "s05al", "filtro testuale", "passo", "consumiwltp 153", "gpl 5", "c450", "consumo2stimato", "capacitabagagliao2dm", "<PERSON><PERSON><PERSON><PERSON> 188", "servizio 08", "zata 66", "16v tfsi", "20v", "105cv", "uc05", "trazione", "evo2", "dati", "commerciante", "getequipaggiamentilistino 31", "codice 1", "ai 100", "grafiche", "a 4mbps", "odparam 249", "web 381", "deumidificato 4639", "il 2000", "radiat 3698", "awebmethodresult 280", "capacitabagagliao3dm", "incapacitabagagliaio2dm", "fleet 4", "eta", "cliente 5", "entro 4", "presenti 10", "di 4", "a 990", "sempre", "almeno 1", "w003", "thod<PERSON>am 206", "sistema 2", "carrozzeria", "ethodparam 148", "estazioni 121", "jt 01", "q501v", "getpianitagliando 57", "supporto 864", "filtro di", "it 3", "cus<PERSON><PERSON> 1807", "quattroruote 378", "veicolo 1", "autoscout 24", "icla03", "complessivo", "vettura", "ruo<PERSON> 834", "tpms 2", "servicemodel 9", "8ysbdc", "038130079qx", "filtro aria", "arrivo", "targa", "aa00000a", "getcodiciriparazioneconsimili19", "getquotazionepersonalizzata 71", "finanziario 3", "i 120gg", "s05aq", "service 4", "terzi 0", "veicolo 7", "rinnovo 4", "odresult 150", "s0481", "riv50", "versione", "preventivi 33", "utente 43", "system 2", "nel 1995", "user", "consegna 7", "50x", "sistema complessivo", "in 75", "vgt 2wd", "batteria una", "tool 3", "80kw", "s0488", "ricambio 298", "ven64", "disponibile 2", "uc 05", "modelliwebmethodparam 252", "permuta", "q018cp", "iw4", "w007", "a 360", "modello", "amministrazione 4", "figura 2", "registrazione 2", "incapacitabagagliaio1dmstimat", "co2extrahigh", "policy 46", "s04kc", "s08ka", "di 0", "datoanagrafica 179", "100km per", "getinstantwebdata 46", "anagrafica 1", "arrayofricambio 124", "svgcarrozzeriafile 302", "futuri 13", "modifica 06", "versione 2", "e 30", "eclipse 2", "massa", "formato", "da 3", "meccanico 5", "c 3724", "version16", "lega 836", "dettagliata", "utilities 3", "xml 348", "faq finale", "<PERSON>ia nella", "removed 3", "6ns", "rotazione 299", "getcodicicasa 14", "java 326", "pure 700", "k55112789ad", "metodologiche 373", "result 297", "veicolo 5", "crm 4", "produttrice", "getinfoproprieta 45", "lrw 2", "chilogrammetri", "equipaggiamentonormalizzato 219", "co2medium", "dresult 240", "anni 01", "restituito 0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dparam 240", "getscore 1", "arrayofdatoallestimentotelaio 110", "elettrove 590", "manuale 3", "punto 3", "guida 3814", "68u", "s048z", "arrayofdatomarca 113", "suddiv<PERSON>", "lega 5228", "s06ns", "gestionale", "arrayofmotoreprestazioni 120", "infoquotazione 233", "pagina", "arrayofveicolodavin 126", "di 75", "header 13", "s05df", "berlina 2", "aa000aa", "batteria viene", "modulo 254", "ricamb<PERSON>", "utf8encoding", "futuri 380", "capacitabagagliaio3d", "alimentazoine", "prime 4", "valore 0", "a000", "86v", "preventivi 34", "kilometraggio", "codificadati 147", "il 30", "datotecnicocompletowltp 199", "hodresult 274", "centralina comando", "effettivo", "combustibile", "italiano 1", "xml 351", "<PERSON>zzo 48", "sic60", "x64", "numero 02", "s0855", "risoluzione", "a 120", "arrayofdatoallestimento 109", "totale"], "last_update": "2025-08-02T22:05:38.620477"}