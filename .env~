# API Configuration
GOOGLE_API_KEY=AIzaSyB0s3SyszzNmKareEWhuHme6CUFp_rKTjU
GEMINI_MODEL=gemini-2.0-flash
JINA-API-KEY=jina_e75ab0c1b5324a5cbe6376799ffc6414JdkTEwqMAW_oQ1MMSYX3z72n-50I
JINA_TRY_V4_FIRST=false

# Paths Configuration  
SORGENTI_PATH=./sorgenti
CACHE_PATH=./cache
LOG_PATH=./logs

# Performance Settings
MAX_CONTEXT_TOKENS=8000
CHUNK_SIZE=1000
OVERLAP_SIZE=200
CACHE_TTL=3600

# Security Settings
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=.pdf
MAX_CONCURRENT_DOCS=10

# Domain Configuration
# Available options: AUTOMOTIVE, FINANCE, HR, IT_SUPPORT, HEALTHCARE, LEGAL
# Single domain: SYSTEM_SCOPE=AUTOMOTIVE
# Multiple domains: SYSTEM_SCOPE=AUTOMOTIVE,FINANCE,HR
SYSTEM_SCOPE=AUTOMOTIVE

# Database Configuration (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=softway_chat
DB_USER=prova
DB_PASSWORD=prova
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_CONNECTION_TIMEOUT=30
DB_POOL_SIZE=5

# Advanced Cache Configuration
CACHE_TYPE=filesystem
CACHE_MAX_SIZE_MB=500
CACHE_TTL_HOURS=24
CACHE_CLEANUP_INTERVAL_HOURS=6

# Redis Cache Configuration (if using Redis)
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_KEY_PREFIX=docache:

# MCP Server Settings
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8080
MCP_TIMEOUT=30

# Gemini-specific Settings
GEMINI_TEMPERATURE=0.2
GEMINI_MAX_OUTPUT_TOKENS=2048
GEMINI_TOP_P=0.8
GEMINI_TOP_K=40

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# Domain-Specific Examples:
#
# Single Domain Examples:
# SYSTEM_SCOPE=AUTOMOTIVE          # Automotive/Mechanical Support
# SYSTEM_SCOPE=FINANCE             # Financial Services
# SYSTEM_SCOPE=HR                  # Human Resources
# SYSTEM_SCOPE=IT_SUPPORT          # IT Technical Support
# SYSTEM_SCOPE=HEALTHCARE          # Healthcare Information
# SYSTEM_SCOPE=LEGAL               # Legal & Compliance
#
# Multiple Domain Examples:
# SYSTEM_SCOPE=AUTOMOTIVE,FINANCE  # Automotive + Finance
# SYSTEM_SCOPE=HR,LEGAL            # HR + Legal
# SYSTEM_SCOPE=IT_SUPPORT,FINANCE,HR  # IT + Finance + HR
# SYSTEM_SCOPE=AUTOMOTIVE,IT_SUPPORT,HEALTHCARE  # Technical multi-domain
