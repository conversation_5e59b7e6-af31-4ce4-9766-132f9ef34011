# Guida alla Configurazione SYSTEM_SCOPE

## Panoramica

Il sistema AI chatbot è ora configurabile per operare in diversi domini applicativi tramite la variabile d'ambiente `SYSTEM_SCOPE`. Questa funzionalità permette di adattare il comportamento, la terminologia e il perimetro di conoscenza del sistema a settori specifici.

## Configurazione Base

### 1. File .env

Aggiungi o modifica la seguente variabile nel file `.env`:

```env
# Domain Configuration - Single Domain
SYSTEM_SCOPE=AUTOMOTIVE

# Domain Configuration - Multiple Domains
SYSTEM_SCOPE=AUTOMOTIVE,FINANCE,HR
```

### 2. <PERSON><PERSON> vs Multipli

Il sistema supporta sia **domini singoli** che **domini multipli**:

- **Dom<PERSON>o <PERSON>**: `SYSTEM_SCOPE=AUTOMOTIVE`
- **<PERSON><PERSON> Multipli**: `SYSTEM_SCOPE=AUTOMOTIVE,FINANCE` (separati da virgola)
- **Spazi**: G<PERSON> spazi attorno alle virgole vengono ignorati: `AUTOMOTIVE, FINANCE, HR`

### 3. Domini Disponibili

Il sistema supporta attualmente i seguenti domini:

| Codice | Nome Completo | Descrizione |
|--------|---------------|-------------|
| `AUTOMOTIVE` | Automotive & Mechanical | Assistenza tecnica per veicoli a motore |
| `FINANCE` | Finance & Banking | Servizi finanziari, bancari e assicurativi |
| `HR` | Human Resources | Gestione risorse umane e personale |
| `IT_SUPPORT` | IT Support & Technical | Supporto tecnico informatico |
| `HEALTHCARE` | Healthcare & Medical | Informazioni sanitarie e procedure mediche |
| `LEGAL` | Legal & Compliance | Informazioni legali e conformità normativa |

## Configurazioni Multi-Dominio

### Vantaggi dei Domini Multipli

- **Competenza Estesa**: Il sistema può rispondere a domande di più settori
- **Terminologia Combinata**: Keywords di tutti i domini attivi
- **Validazione Intelligente**: Accetta query relative a qualsiasi dominio configurato
- **Prompt Adattivi**: L'AI si adatta automaticamente al tipo di domanda

### Esempi di Combinazioni Utili

```env
# Business Completo
SYSTEM_SCOPE=FINANCE,HR,LEGAL

# Supporto Tecnico Esteso
SYSTEM_SCOPE=IT_SUPPORT,AUTOMOTIVE

# Consulenza Aziendale
SYSTEM_SCOPE=HR,LEGAL,FINANCE

# Supporto Multi-Settore
SYSTEM_SCOPE=AUTOMOTIVE,IT_SUPPORT,FINANCE,HR
```

### Come Funziona la Combinazione

1. **Keywords Unite**: Tutte le keywords dei domini vengono combinate
2. **Non-Keywords Filtrate**: Evita conflitti tra domini
3. **Prompt Intelligente**: L'AI identifica automaticamente il dominio della domanda
4. **Validazione Estesa**: Accetta query di qualsiasi dominio configurato

## Configurazioni Specifiche per Dominio

### AUTOMOTIVE (Default)
```env
SYSTEM_SCOPE=AUTOMOTIVE
```

**Caratteristiche:**
- Terminologia tecnica automotive e meccanica
- Supporto per motocicli, scooter, automobili
- Procedure di manutenzione e riparazione
- Diagnostica e codici errore
- Specifiche tecniche e coppie di serraggio

**Esempio di risposta:** "Per sostituire le pastiglie freni anteriori del tuo scooter, dovrai rimuovere la pinza freno svitando i bulloni di fissaggio con una coppia di 25 Nm..."

### FINANCE
```env
SYSTEM_SCOPE=FINANCE
```

**Caratteristiche:**
- Terminologia finanziaria e bancaria
- Prodotti di investimento e assicurativi
- Procedure bancarie e normative
- Analisi di mercato e strumenti finanziari
- Fiscalità e dichiarazioni

**Esempio di risposta:** "Il tasso di interesse variabile è legato all'Euribor e può fluttuare in base alle condizioni di mercato. Prima di sottoscrivere un mutuo a tasso variabile..."

### HR (Human Resources)
```env
SYSTEM_SCOPE=HR
```

**Caratteristiche:**
- Gestione del personale e recruiting
- Contratti di lavoro e normative
- Formazione e sviluppo carriera
- Valutazione performance
- Procedure disciplinari e amministrative

**Esempio di risposta:** "Il processo di onboarding per un nuovo dipendente dovrebbe includere la preparazione della documentazione contrattuale, l'assegnazione degli strumenti di lavoro..."

### IT_SUPPORT
```env
SYSTEM_SCOPE=IT_SUPPORT
```

**Caratteristiche:**
- Supporto tecnico hardware e software
- Troubleshooting e diagnostica IT
- Configurazioni di rete e sicurezza
- Procedure di backup e recovery
- Ottimizzazione performance sistemi

**Esempio di risposta:** "Per risolvere il problema di connettività, verifica prima la configurazione IP con il comando 'ipconfig /all' e controlla che il gateway predefinito..."

### HEALTHCARE
```env
SYSTEM_SCOPE=HEALTHCARE
```

**Caratteristiche:**
- Informazioni sanitarie generali
- Procedure mediche e protocolli
- Terminologia medica specialistica
- Linee guida cliniche
- **IMPORTANTE:** Non fornisce diagnosi o consigli medici specifici

**Esempio di risposta:** "La procedura di sterilizzazione degli strumenti chirurgici prevede diverse fasi. È fondamentale consultare sempre le linee guida del Ministero della Salute..."

### LEGAL
```env
SYSTEM_SCOPE=LEGAL
```

**Caratteristiche:**
- Informazioni normative e legislative
- Procedure legali e compliance
- Diritto civile, penale, amministrativo
- Contrattualistica e società
- **IMPORTANTE:** Non fornisce consigli legali specifici

**Esempio di risposta:** "Secondo l'articolo 1341 del Codice Civile, le clausole vessatorie devono essere specificamente approvate per iscritto. Si consiglia di consultare un avvocato..."

## Implementazione Tecnica

### 1. Whitelist Dinamica

Il sistema mantiene la funzionalità di whitelist dinamica che estrae automaticamente termini dalla documentazione. Questa funziona in combinazione con le parole chiave del dominio configurato.

### 2. Validazione Query

Le query vengono validate contro:
1. **Keywords del dominio**: Termini specifici del settore
2. **Non-keywords**: Termini che indicano altri domini
3. **Whitelist documentale**: Termini estratti dai documenti PDF
4. **Pattern tecnici**: Espressioni regolari specifiche del dominio

### 3. Messaggi di Rifiuto Personalizzati

Ogni dominio ha un messaggio di rifiuto specifico che guida l'utente verso il tipo di domande appropriate.

## Esempi Pratici

### Cambio da Automotive a Finance

1. **Modifica .env:**
```env
# Prima
SYSTEM_SCOPE=AUTOMOTIVE

# Dopo
SYSTEM_SCOPE=FINANCE
```

### Configurazione Multi-Dominio

1. **Aggiungere domini multipli:**
```env
# Da singolo a multiplo
SYSTEM_SCOPE=AUTOMOTIVE,FINANCE

# Tre domini
SYSTEM_SCOPE=HR,LEGAL,IT_SUPPORT
```

2. **Utilizzare utility script:**
```bash
# Cambio a domini multipli
python switch_domain.py --switch "AUTOMOTIVE,FINANCE"

# Verifica configurazione
python switch_domain.py --current
```

3. **Test configurazione multi-dominio:**
```bash
# Query automotive (accettata)
"Come cambio l'olio del motore?"

# Query finance (accettata)
"Come funziona un mutuo a tasso fisso?"

# Query fuori dominio (rifiutata)
"Come cucinare la pasta?"
```

### Configurazione Multi-Ambiente

Per gestire diversi ambienti (sviluppo, test, produzione):

```bash
# Sviluppo - .env.development
SYSTEM_SCOPE=AUTOMOTIVE

# Test - .env.test  
SYSTEM_SCOPE=IT_SUPPORT

# Produzione - .env.production
SYSTEM_SCOPE=FINANCE
```

## Personalizzazione Avanzata

### Aggiungere un Nuovo Dominio

1. **Modifica `domain_config.py`:**
```python
'EDUCATION': DomainConfig(
    name='EDUCATION',
    display_name='Education & Training',
    description='Educational content and training materials',
    keywords={
        'scuola', 'università', 'corso', 'lezione', 'studente',
        'insegnante', 'professore', 'esame', 'voto', 'diploma'
    },
    non_keywords={
        'motore', 'freni', 'investimento', 'programmazione'
    },
    technical_keywords={
        'didattica', 'pedagogia', 'curriculum', 'competenze'
    },
    system_prompt="""Sei un assistente specializzato in educazione e formazione...""",
    rejection_message="Mi dispiace, ma posso assistere solo con questioni educative..."
)
```

2. **Aggiorna la documentazione**
3. **Testa la nuova configurazione**

### Personalizzazione Keywords

Le keywords possono essere personalizzate modificando il file `domain_config.py`. Ogni dominio ha tre tipi di keywords:

- **keywords**: Termini che identificano il dominio
- **non_keywords**: Termini che escludono altri domini  
- **technical_keywords**: Termini tecnici specialistici

## Monitoraggio e Debug

### Log di Dominio

Il sistema registra nei log quando una query viene accettata o rifiutata:

```
INFO - Query approved by domain: FINANCE (detected: mutuo, tasso, interesse)
INFO - Query outside healthcare domain: come riparo il motore... - Query appears to be outside healthcare domain
```

### Statistiche Whitelist

Usa il comando per verificare lo stato della whitelist:

```bash
python utils/update_whitelist.py stats
```

### Test Configurazione

Verifica che il dominio sia configurato correttamente:

```bash
python test_system.py --domain-check
```

## Best Practices

### 1. Documentazione Specifica

Assicurati che la cartella `sorgenti/` contenga documentazione pertinente al dominio configurato.

### 2. Backup Configurazione

Mantieni backup delle configurazioni per diversi domini:

```bash
cp .env .env.automotive.backup
cp .env .env.finance.backup
```

### 3. Test Graduali

Quando cambi dominio, testa gradualmente con query di complessità crescente:

1. Query semplici con keywords ovvie
2. Query tecniche specifiche
3. Query ai confini del dominio
4. Query che dovrebbero essere rifiutate

### 4. Monitoraggio Continuo

Monitora i log per identificare:
- False positive (query accettate erroneamente)
- False negative (query rifiutate erroneamente)
- Necessità di aggiornare keywords

## Risoluzione Problemi

### Query Rifiutate Erroneamente

1. Verifica le keywords del dominio
2. Controlla la whitelist documentale
3. Aggiorna i termini se necessario

### Query Accettate Erroneamente

1. Aggiungi termini alle non_keywords
2. Raffina i pattern di esclusione
3. Migliora la validazione

### Prestazioni Lente

1. Ottimizza la cache della whitelist
2. Riduci la frequenza di aggiornamento
3. Limita la dimensione dei documenti

## Supporto

Per assistenza sulla configurazione SYSTEM_SCOPE:

1. Consulta i log di sistema
2. Verifica la documentazione tecnica
3. Testa con query di esempio
4. Contatta il team di sviluppo per personalizzazioni avanzate
