class ChatInterface {
    constructor() {
        this.websocket = null;
        this.sessionId = null;
        this.currentProduct = null;
        this.isConnected = false;
        this.messageQueue = [];

        // DOM elements
        this.chatMain = document.querySelector('.chat-main');
        this.textInput = document.querySelector('.text-input');
        this.sendButton = document.querySelector('.send-button');
        this.typingIndicator = document.querySelector('.typing-indicator');
        this.optionsMenuButton = document.getElementById('options-menu-button');
        this.contextMenu = document.getElementById('context-menu');
        this.sourcesModal = document.getElementById('sources-modal');
        this.modalCloseButton = document.getElementById('modal-close-button');

        this.init();
    }

    async init() {
        // Setup event listeners
        this.setupEventListeners();

        // Load available products and show selection
        await this.showProductSelection();

        // Hide typing indicator initially
        this.hideTypingIndicator();
    }

    setupEventListeners() {
        // Send message on button click
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Send message on Enter key
        this.textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Options menu
        this.optionsMenuButton.addEventListener('click', (event) => {
            event.stopPropagation();
            this.contextMenu.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', (event) => {
            if (!this.contextMenu.contains(event.target) && this.contextMenu.classList.contains('active')) {
                this.contextMenu.classList.remove('active');
            }
        });

        // Close menu on Escape
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.contextMenu.classList.contains('active')) {
                this.contextMenu.classList.remove('active');
            }
        });

        // Context menu actions
        this.setupContextMenuActions();

        // Sources modal
        this.setupSourcesModal();
    }

    setupContextMenuActions() {
        const menuItems = this.contextMenu.querySelectorAll('a');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const action = item.textContent.trim();

                switch(action) {
                    case 'Riavvia Chat':
                        this.restartChat();
                        break;
                    case 'Svuota Chat':
                        this.clearChat();
                        break;
                    case 'Esporta Chat':
                        this.exportChat();
                        break;
                    case 'Info':
                        this.showInfo();
                        break;
                }

                this.contextMenu.classList.remove('active');
            });
        });
    }

    setupSourcesModal() {
        this.modalCloseButton.addEventListener('click', () => this.closeSourcesModal());

        window.addEventListener('click', (event) => {
            if (event.target === this.sourcesModal) {
                this.closeSourcesModal();
            }
        });

        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.sourcesModal.classList.contains('active')) {
                this.closeSourcesModal();
            }
        });
    }

    async showProductSelection() {
        try {
            this.showLoadingMessage('Caricamento prodotti disponibili...');

            const response = await fetch('/api/products');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const products = await response.json();

            // Remove loading message
            this.removeLoadingMessage();

            if (products.length === 0) {
                this.addErrorMessage('Nessun prodotto disponibile al momento.');
                return;
            }

            // Add simple selection message
            this.addMessage('bot', 'Seleziona un prodotto per iniziare:');

            // Create product selection buttons
            const buttonsContainer = document.createElement('div');
            buttonsContainer.className = 'product-buttons';
            buttonsContainer.style.cssText = 'display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px;';

            products.forEach(product => {
                const button = document.createElement('button');
                button.textContent = product.display_name;
                button.className = 'product-button';
                button.style.cssText = 'padding: 8px 16px; border: 1px solid #007AFF; background: white; color: #007AFF; border-radius: 16px; cursor: pointer; font-size: 14px;';

                button.addEventListener('click', () => this.selectProduct(product.name));
                button.addEventListener('mouseenter', () => {
                    button.style.backgroundColor = '#007AFF';
                    button.style.color = 'white';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.backgroundColor = 'white';
                    button.style.color = '#007AFF';
                });

                buttonsContainer.appendChild(button);
            });

            // Add buttons to the last bot message
            const lastBotMessage = this.chatMain.querySelector('.chat-bubble.chatbot:last-of-type');
            if (lastBotMessage) {
                lastBotMessage.appendChild(buttonsContainer);
            }

        } catch (error) {
            console.error('Error loading products:', error);
            this.removeLoadingMessage();
            this.addErrorMessage('Errore nel caricamento dei prodotti. Riprova più tardi.', error.message);

            // Add retry button
            this.addRetryButton(() => this.showProductSelection());
        }
    }

    async selectProduct(productName) {
        try {
            this.currentProduct = productName;

            // Remove product selection buttons
            const productButtons = this.chatMain.querySelector('.product-buttons');
            if (productButtons) {
                productButtons.remove();
            }

            // Show loading
            this.showLoadingMessage(`Inizializzazione sessione per ${productName.replace('_', ' ')}...`);

            // Create session
            const response = await fetch('/api/session/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ product: productName })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            }

            const sessionData = await response.json();
            this.sessionId = sessionData.session_id;

            // Connect WebSocket
            await this.connectWebSocket();

            // Remove loading message
            this.removeLoadingMessage();

            // Update UI
            document.querySelector('.title').textContent = `Chatbot - ${productName.replace('_', ' ')}`;
            this.addMessage('bot', `Perfetto! Hai selezionato ${productName.replace('_', ' ')}. Come posso aiutarti?`);

            // Enable input
            this.textInput.disabled = false;
            this.textInput.placeholder = 'Scrivi il tuo messaggio...';
            this.sendButton.disabled = false;

        } catch (error) {
            console.error('Error selecting product:', error);
            this.removeLoadingMessage();
            this.addErrorMessage('Errore nella selezione del prodotto.', error.message);

            // Add retry button
            this.addRetryButton(() => this.selectProduct(productName));
        }
    }

    async connectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/${this.sessionId}`;

        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('WebSocket connected');
            this.isConnected = true;

            // Send queued messages
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                this.websocket.send(JSON.stringify(message));
            }
        };

        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };

        this.websocket.onclose = () => {
            console.log('WebSocket disconnected');
            this.isConnected = false;
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.addErrorMessage('Errore di connessione WebSocket.', 'La connessione in tempo reale non è disponibile.');
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'typing':
                if (data.status === 'processing') {
                    this.showTypingIndicator('🤔 Sto analizzando la tua domanda...');
                } else if (data.status === 'searching') {
                    this.updateThinkingMessage('🔍 Cerco nelle documentazioni...');
                } else if (data.status === 'generating') {
                    this.updateThinkingMessage('✍️ Sto preparando la risposta...');
                }
                break;

            case 'message':
                this.hideTypingIndicator();
                this.addMessage('bot', data.response, data.sources);
                // Re-enable input
                this.textInput.disabled = false;
                this.sendButton.disabled = false;
                break;

            case 'error':
                this.hideTypingIndicator();
                this.addErrorMessage('Errore durante l\'elaborazione', data.message);
                // Re-enable input
                this.textInput.disabled = false;
                this.sendButton.disabled = false;
                break;

            case 'progress':
                if (data.message) {
                    this.updateThinkingMessage(data.message);
                }
                break;

            default:
                console.log('Unknown message type:', data.type);
        }
    }

    sendMessage() {
        const message = this.textInput.value.trim();
        if (!message) return;

        // Add user message to chat
        this.addMessage('user', message);

        // Show thinking indicator immediately
        this.showTypingIndicator('🤔 Sto elaborando la tua richiesta...');

        // Clear input
        this.textInput.value = '';

        // Disable input while processing
        this.textInput.disabled = true;
        this.sendButton.disabled = true;

        // Send via WebSocket
        const messageData = {
            message: message,
            product: this.currentProduct,
            session_id: this.sessionId
        };

        if (this.isConnected && this.websocket) {
            this.websocket.send(JSON.stringify(messageData));
        } else {
            // Queue message if not connected
            this.messageQueue.push(messageData);
            this.updateThinkingMessage('🔄 Connessione in corso...');
        }

        // Re-enable input after a delay (in case of errors)
        setTimeout(() => {
            this.textInput.disabled = false;
            this.sendButton.disabled = false;
        }, 30000); // 30 seconds timeout
    }

    addMessage(sender, content, sources = []) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-bubble ${sender === 'user' ? 'user' : 'chatbot'}`;

        // Format content with markdown if it's from bot
        if (sender === 'chatbot' || sender === 'bot') {
            messageDiv.innerHTML = this.formatMarkdown(content);
        } else {
            messageDiv.textContent = content;
        }

        // Add sources button if sources are provided
        if (sources && sources.length > 0) {
            const sourcesButton = document.createElement('div');
            sourcesButton.className = 'sources-icon';
            sourcesButton.title = `${sources.length} fonte${sources.length > 1 ? 'i' : ''} disponibile${sources.length > 1 ? 'i' : ''}`;
            sourcesButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-text" viewBox="0 0 16 16">
                    <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                    <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2.5h2.5L9.5 1zM3 2.5a.5.5 0 0 1 .5-.5H9v2.5A1.5 1.5 0 0 0 10.5 6H13v8a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2.5z"/>
                </svg>
                <span class="sources-count">${sources.length}</span>
            `;
            sourcesButton.addEventListener('click', () => this.showSources(sources));
            messageDiv.appendChild(sourcesButton);
        }

        // Remove typing indicator before adding message
        this.hideTypingIndicator();

        // Add message to chat
        this.chatMain.appendChild(messageDiv);

        // Scroll to bottom
        this.scrollToBottom();
    }

    formatMarkdown(text) {
        // Simple markdown formatting
        return text
            // Bold text **text** or __text__
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<strong>$1</strong>')
            // Italic text *text* or _text_
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/_(.*?)_/g, '<em>$1</em>')
            // Line breaks
            .replace(/\n/g, '<br>')
            // Lists (simple implementation)
            .replace(/^- (.*$)/gim, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
            // Numbers in lists
            .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/s, '<ol>$1</ol>');
    }

    showSources(sources) {
        const sourcesList = this.sourcesModal.querySelector('.sources-list');
        sourcesList.innerHTML = '';

        sources.forEach(source => {
            const li = document.createElement('li');
            const a = document.createElement('a');

            // Use the URL from the source if available, otherwise create a placeholder
            if (source.url) {
                a.href = source.url;
                a.target = '_blank';  // Open in new tab
                a.rel = 'noopener noreferrer';  // Security
            } else {
                a.href = '#';
            }

            a.innerHTML = `${source.filename} <span class="page-number">pag. ${source.page}</span>`;

            // Add click handler
            a.addEventListener('click', (e) => {
                if (source.url) {
                    // Let the browser handle the link naturally
                    console.log('Opening PDF:', source.filename, 'page', source.page);
                } else {
                    e.preventDefault();
                    console.log('No URL available for:', source);
                    alert('Link al documento non disponibile');
                }
            });

            li.appendChild(a);
            sourcesList.appendChild(li);
        });

        this.sourcesModal.style.display = 'flex';
        setTimeout(() => this.sourcesModal.classList.add('active'), 10);
    }

    closeSourcesModal() {
        this.sourcesModal.classList.remove('active');
        setTimeout(() => this.sourcesModal.style.display = 'none', 300);
    }

    showTypingIndicator(message = 'Il sistema sta elaborando...') {
        // Remove any existing thinking indicators
        this.hideTypingIndicator();

        // Create enhanced thinking indicator
        const thinkingDiv = document.createElement('div');
        thinkingDiv.className = 'chat-bubble chatbot thinking-indicator';
        thinkingDiv.setAttribute('data-thinking', 'true');

        thinkingDiv.innerHTML = `
            <div class="thinking-content">
                <div class="thinking-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <div class="thinking-text">${message}</div>
            </div>
        `;

        this.chatMain.appendChild(thinkingDiv);
        this.scrollToBottom();
    }

    updateThinkingMessage(message) {
        const thinkingIndicator = this.chatMain.querySelector('[data-thinking="true"]');
        if (thinkingIndicator) {
            const textElement = thinkingIndicator.querySelector('.thinking-text');
            if (textElement) {
                textElement.textContent = message;
            }
        }
    }

    hideTypingIndicator() {
        // Remove old typing indicator
        this.typingIndicator.style.display = 'none';

        // Remove thinking indicators
        const thinkingIndicators = this.chatMain.querySelectorAll('[data-thinking="true"]');
        thinkingIndicators.forEach(indicator => indicator.remove());
    }

    scrollToBottom() {
        this.chatMain.scrollTop = this.chatMain.scrollHeight;
    }

    clearChat() {
        // Keep only the welcome message
        const messages = this.chatMain.querySelectorAll('.chat-bubble');
        messages.forEach((message, index) => {
            if (index > 0) { // Keep first message (welcome)
                message.remove();
            }
        });

        // Reset state
        this.currentProduct = null;
        this.sessionId = null;
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        this.isConnected = false;

        // Disable input
        this.textInput.disabled = true;
        this.textInput.placeholder = 'Seleziona un prodotto per iniziare...';
        this.sendButton.disabled = true;

        // Reset title
        document.querySelector('.title').textContent = 'Chatbot';

        // Show product selection again (only if not already showing)
        if (!this.chatMain.querySelector('.product-buttons')) {
            this.showProductSelection();
        }
    }

    async restartChat() {
        if (confirm('Sei sicuro di voler riavviare la chat? Tutti i messaggi verranno persi.')) {
            this.clearChat();
        }
    }

    exportChat() {
        const messages = this.chatMain.querySelectorAll('.chat-bubble');
        let chatText = `Chat Export - ${new Date().toLocaleString()}\n\n`;

        messages.forEach(message => {
            const sender = message.classList.contains('user') ? 'Tu' : 'Chatbot';
            const content = message.textContent.trim();
            chatText += `${sender}: ${content}\n\n`;
        });

        const blob = new Blob([chatText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-export-${Date.now()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    showInfo() {
        const info = `
Chatbot Tecnico - Informazioni

Versione: 1.0.0
Prodotto attuale: ${this.currentProduct || 'Nessuno'}
Sessione: ${this.sessionId || 'Nessuna'}
Connessione: ${this.isConnected ? 'Attiva' : 'Non attiva'}

Comandi disponibili:
- Riavvia Chat: Ricomincia da capo
- Svuota Chat: Cancella i messaggi
- Esporta Chat: Salva la conversazione
        `.trim();

        alert(info);
    }

    showLoadingMessage(message) {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'chat-bubble chatbot loading-message';
        loadingDiv.textContent = message;
        loadingDiv.setAttribute('data-loading', 'true');

        this.chatMain.appendChild(loadingDiv);
        this.scrollToBottom();
    }

    removeLoadingMessage() {
        const loadingMessages = this.chatMain.querySelectorAll('[data-loading="true"]');
        loadingMessages.forEach(msg => msg.remove());
    }

    addErrorMessage(title, details = '') {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'chat-bubble chatbot error-message';

        let content = title;
        if (details) {
            content += `\n\nDettagli: ${details}`;
        }

        errorDiv.textContent = content;
        this.chatMain.appendChild(errorDiv);
        this.scrollToBottom();
    }

    addRetryButton(retryFunction) {
        const retryDiv = document.createElement('div');
        retryDiv.style.cssText = 'text-align: center; margin: 10px 0;';

        const retryButton = document.createElement('button');
        retryButton.textContent = 'Riprova';
        retryButton.className = 'product-button';
        retryButton.style.cssText = 'background: #FF3B30; border-color: #FF3B30; color: white;';

        retryButton.addEventListener('click', () => {
            retryDiv.remove();
            retryFunction();
        });

        retryButton.addEventListener('mouseenter', () => {
            retryButton.style.backgroundColor = '#D70015';
        });

        retryButton.addEventListener('mouseleave', () => {
            retryButton.style.backgroundColor = '#FF3B30';
        });

        retryDiv.appendChild(retryButton);
        this.chatMain.appendChild(retryDiv);
        this.scrollToBottom();
    }

    // Enhanced connection monitoring
    monitorConnection() {
        setInterval(() => {
            if (this.websocket && this.websocket.readyState === WebSocket.CLOSED) {
                this.isConnected = false;
                if (this.sessionId) {
                    this.addErrorMessage('Connessione persa', 'Tentativo di riconnessione...');
                    this.connectWebSocket();
                }
            }
        }, 5000); // Check every 5 seconds
    }
}

// Initialize chat interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const chatInterface = new ChatInterface();
    chatInterface.monitorConnection();
});
