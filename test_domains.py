#!/usr/bin/env python3
"""
Test script for domain configuration functionality.
Tests different SYSTEM_SCOPE settings and validates domain-specific behavior.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from domain_config import get_domain_config, get_multi_domain_config, get_available_domains, validate_domain
from security_utils import DomainRelevanceValidator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DomainTester:
    """Test domain configuration functionality"""
    
    def __init__(self):
        self.test_queries = {
            'AUTOMOTIVE': [
                "Come cambio l'olio del motore?",
                "Qual è la coppia di serraggio dei bulloni?",
                "Il motore fa un rumore strano",
                "Come sostituire le pastiglie freni?"
            ],
            'FINANCE': [
                "Come funziona un mutuo a tasso fisso?",
                "Quali sono le commissioni bancarie?",
                "Come investire in ETF?",
                "Cos'è lo spread sui prestiti?"
            ],
            'HR': [
                "Come gestire un colloquio di lavoro?",
                "Quali sono i diritti del lavoratore?",
                "Come calcolare le ferie maturate?",
                "Procedura per licenziamento disciplinare"
            ],
            'IT_SUPPORT': [
                "Come configurare una VPN?",
                "Il computer non si avvia",
                "Errore di connessione di rete",
                "Come fare backup del database?"
            ],
            'HEALTHCARE': [
                "Procedura per sterilizzazione strumenti",
                "Protocollo emergenza cardiaca",
                "Come gestire un paziente diabetico",
                "Linee guida igiene ospedaliera"
            ],
            'LEGAL': [
                "Come redigere un contratto di lavoro?",
                "Normativa sulla privacy GDPR",
                "Procedura per costituire una SRL",
                "Diritti del consumatore"
            ]
        }
        
        self.rejection_queries = {
            'AUTOMOTIVE': [
                "Come cucinare la pasta?",
                "Investimenti in borsa",
                "Configurazione server Linux"
            ],
            'FINANCE': [
                "Come riparare il motore?",
                "Ricetta per la pizza",
                "Installare Windows 11"
            ],
            'IT_SUPPORT': [
                "Cambio olio motore",
                "Mutuo casa",
                "Ricetta tiramisù"
            ]
        }
    
    def test_domain_config_loading(self):
        """Test domain configuration loading"""
        print("🔧 Testing domain configuration loading...")
        
        # Test available domains
        domains = get_available_domains()
        print(f"✅ Available domains: {', '.join(domains)}")
        
        # Test each domain configuration
        for domain in domains:
            try:
                domain_config = get_domain_config(domain)
                print(f"✅ {domain}: {domain_config.display_name}")
                print(f"   Keywords: {len(domain_config.keywords)}")
                print(f"   Non-keywords: {len(domain_config.non_keywords)}")
                print(f"   Technical keywords: {len(domain_config.technical_keywords)}")
            except Exception as e:
                print(f"❌ {domain}: Error loading config - {e}")
        
        # Test invalid domain
        try:
            invalid_config = get_domain_config("INVALID_DOMAIN")
            print(f"⚠️  Invalid domain fallback: {invalid_config.name}")
        except Exception as e:
            print(f"❌ Invalid domain handling failed: {e}")
    
    def test_domain_validation(self):
        """Test domain validation functionality"""
        print("\n🔍 Testing domain validation...")
        
        # Test validation function
        valid_domains = ['AUTOMOTIVE', 'FINANCE', 'HR']
        invalid_domains = ['INVALID', 'TEST', '']
        
        for domain in valid_domains:
            result = validate_domain(domain)
            print(f"✅ {domain}: {'Valid' if result else 'Invalid'}")
        
        for domain in invalid_domains:
            result = validate_domain(domain)
            print(f"❌ {domain}: {'Valid' if result else 'Invalid'}")
    
    async def test_query_validation(self, domain: str):
        """Test query validation for specific domain"""
        print(f"\n🎯 Testing query validation for {domain}...")
        
        # Temporarily set the domain (for testing purposes)
        original_scope = os.environ.get('SYSTEM_SCOPE', 'AUTOMOTIVE')
        os.environ['SYSTEM_SCOPE'] = domain
        
        try:
            # Create new validator with the test domain
            validator = DomainRelevanceValidator()
            
            # Test positive queries (should be accepted)
            if domain in self.test_queries:
                print(f"  Positive queries for {domain}:")
                for query in self.test_queries[domain]:
                    is_valid, error = validator.validate_domain_relevance(query)
                    status = "✅ ACCEPTED" if is_valid else f"❌ REJECTED: {error}"
                    print(f"    '{query[:40]}...' -> {status}")
            
            # Test negative queries (should be rejected)
            if domain in self.rejection_queries:
                print(f"  Negative queries for {domain}:")
                for query in self.rejection_queries[domain]:
                    is_valid, error = validator.validate_domain_relevance(query)
                    status = "❌ ACCEPTED (ERROR!)" if is_valid else "✅ REJECTED (CORRECT)"
                    print(f"    '{query[:40]}...' -> {status}")
        
        finally:
            # Restore original scope
            os.environ['SYSTEM_SCOPE'] = original_scope
    
    def test_current_configuration(self):
        """Test current system configuration"""
        print(f"\n⚙️  Testing current configuration...")
        print(f"Current SYSTEM_SCOPE: {config.system_scope}")
        
        try:
            current_config = get_domain_config(config.system_scope)
            print(f"✅ Domain: {current_config.display_name}")
            print(f"✅ Description: {current_config.description}")
            print(f"✅ Keywords count: {len(current_config.keywords)}")
            print(f"✅ Rejection message: {current_config.rejection_message[:100]}...")
        except Exception as e:
            print(f"❌ Error loading current configuration: {e}")

    def test_multi_domain_configuration(self):
        """Test multi-domain configuration functionality"""
        print(f"\n🔗 Testing multi-domain configuration...")

        # Test combinations
        test_combinations = [
            ['AUTOMOTIVE', 'FINANCE'],
            ['HR', 'LEGAL'],
            ['IT_SUPPORT', 'HEALTHCARE'],
            ['AUTOMOTIVE', 'IT_SUPPORT', 'FINANCE']
        ]

        for domains in test_combinations:
            try:
                combined_config = get_multi_domain_config(domains)
                print(f"✅ {'+'.join(domains)}: {combined_config.display_name}")
                print(f"   Keywords: {len(combined_config.keywords)}")
                print(f"   Non-keywords: {len(combined_config.non_keywords)}")

                # Test that keywords from all domains are included
                individual_keywords = set()
                for domain in domains:
                    domain_config = get_domain_config(domain)
                    individual_keywords.update(domain_config.keywords)

                # Check if combined keywords include all individual keywords
                missing_keywords = individual_keywords - combined_config.keywords
                if missing_keywords:
                    print(f"   ⚠️  Missing keywords: {len(missing_keywords)}")
                else:
                    print(f"   ✅ All individual keywords included")

            except Exception as e:
                print(f"❌ {'+'.join(domains)}: Error - {e}")

    async def test_multi_domain_validation(self):
        """Test validation with multiple domains"""
        print(f"\n🎯 Testing multi-domain validation...")

        # Test AUTOMOTIVE + FINANCE combination
        test_domains = ['AUTOMOTIVE', 'FINANCE']
        original_scope = os.environ.get('SYSTEM_SCOPE', 'AUTOMOTIVE')
        os.environ['SYSTEM_SCOPE'] = ','.join(test_domains)

        try:
            validator = DomainRelevanceValidator()

            # Test queries that should be accepted (from both domains)
            automotive_queries = [
                "Come cambio l'olio del motore?",
                "Coppia di serraggio bulloni"
            ]

            finance_queries = [
                "Come funziona un mutuo?",
                "Tasso di interesse variabile"
            ]

            print(f"  Testing automotive queries:")
            for query in automotive_queries:
                is_valid, error = validator.validate_domain_relevance(query)
                status = "✅ ACCEPTED" if is_valid else f"❌ REJECTED: {error}"
                print(f"    '{query}' -> {status}")

            print(f"  Testing finance queries:")
            for query in finance_queries:
                is_valid, error = validator.validate_domain_relevance(query)
                status = "✅ ACCEPTED" if is_valid else f"❌ REJECTED: {error}"
                print(f"    '{query}' -> {status}")

            # Test queries that should be rejected (outside both domains)
            rejection_queries = [
                "Come cucinare la pasta?",
                "Ricetta per pizza",
                "Installare Linux"
            ]

            print(f"  Testing rejection queries:")
            for query in rejection_queries:
                is_valid, error = validator.validate_domain_relevance(query)
                status = "❌ ACCEPTED (ERROR!)" if is_valid else "✅ REJECTED (CORRECT)"
                print(f"    '{query}' -> {status}")

        finally:
            os.environ['SYSTEM_SCOPE'] = original_scope

    async def run_all_tests(self):
        """Run all domain tests"""
        print("🚀 Starting Domain Configuration Tests\n")
        
        # Test 1: Configuration loading
        self.test_domain_config_loading()
        
        # Test 2: Domain validation
        self.test_domain_validation()
        
        # Test 3: Current configuration
        self.test_current_configuration()
        
        # Test 4: Multi-domain configuration
        self.test_multi_domain_configuration()

        # Test 5: Multi-domain validation
        await self.test_multi_domain_validation()

        # Test 6: Query validation for each domain
        test_domains = ['AUTOMOTIVE', 'FINANCE', 'IT_SUPPORT']
        for domain in test_domains:
            await self.test_query_validation(domain)

        print("\n✅ All domain tests completed (including multi-domain tests)!")

async def main():
    """Main test function"""
    tester = DomainTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
